<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmAnchorOperateMapper">


    <select id="existsByAnchorIdAndUserId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM
            crm_anchor_operate ao
                INNER JOIN yooa_system.sys_user_pd up ON ao.operate_id = up.pd_user_id
        WHERE
            ao.anchor_id = #{anchorId}
          AND up.user_id = #{userId}
          AND up.type = 2
    </select>
</mapper>
