<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmAnchorRefundMapper">

    <select id="getRefundRecord" resultType="com.yooa.crm.api.domain.vo.CustomerRefundRecordVo">
        SELECT
            cr.customer_id customerId,
            cr.refund_time refundTime,
            cr.record_id orderNo,
            cre.total_amount totalAmount,
            calr.start_time liveTime
        FROM
            crm_customer_refund cr
                JOIN crm_anchor ca ON ca.anchor_id = cr.tou_id
                LEFT JOIN crm_customer_reward cre ON cr.record_id = cre.id
                LEFT JOIN crm_anchor_live_record calr ON cre.show_id = calr.show_id
        <where>
            cr.tou_id = #{anchorId}
        </where>
        ORDER BY
            liveTime DESC
    </select>
    <select id="getRefundCustomerIds" resultType="com.yooa.crm.api.domain.vo.CustomerRefundRecordVo">
        SELECT DISTINCT
            cr.customer_id customerId,
            cr.record_id orderNo,
            cr.refund_time refundTime,
            are.add_time addTime,
            are.total_amount refundAmount,
            alr.start_time liveTime
        FROM
            crm_anchor_account_mapping aam
                INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
                INNER JOIN crm_customer_refund cr ON cr.anchor_id = aam.account_id
                LEFT JOIN crm_customer_reward are ON are.id = cr.record_id
                LEFT JOIN crm_anchor_live_record alr ON alr.show_id = are.show_id
        WHERE
            ai.anchor_id = #{anchorId}
        order by
            liveTime desc
    </select>
</mapper>
