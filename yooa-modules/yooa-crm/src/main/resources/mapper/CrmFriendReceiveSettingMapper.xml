<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmFriendReceiveSettingMapper">

    <resultMap id="BaseResultMap" type="com.yooa.crm.api.domain.CrmFriendReceiveSetting">
            <id property="receiveId" column="receive_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="receiveNumber" column="receive_number" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        receive_id,dept_id,user_id,
        receive_number,create_time,create_by,
        update_time,update_by
    </sql>
</mapper>
