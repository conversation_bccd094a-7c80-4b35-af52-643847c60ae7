package com.yooa.crm.listen.dts;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.CrmAnchorOperate;
import com.yooa.crm.api.domain.CrmCustomerAscription;
import com.yooa.crm.service.AnchorOperateService;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.CustomerAscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentAdminAndLiveListener extends AbstractBaseListener {

    private final AnchorService anchorService;

    private final AnchorOperateService anchorOperateService;

    private final CustomerAscriptionService customerAscriptionService;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_AGENT_ADMIN_AND_LIVE;
    }

//    @Override
//    public String getBusTopic() {
//        return KafkaBusinessTopicConstants.BUS_CMF_AGENT_ADMIN_AND_LIVE;
//    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();
    }

    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("id", "live_uid", "admin_id");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveAnchorOperate(fieldMap);
    }


    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveAnchorOperate(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
    }

    private void saveAnchorOperate(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        // TODO 单独保存客户归属信息 以后做优化需要用到
        // saveOrUpdateCustomerAscription(fieldMap);

        // 主播id
        String anchorId = getAfterValueByFieldName("live_uid", fieldMap);
        // 运营id
        String operateId = getAfterValueByFieldName("admin_id", fieldMap);

        // 如果之前存在记录 更新脱手时间
        CrmAnchorOperate crmAnchorOperate = Optional.ofNullable(anchorOperateService.getAnchorOperateByAnchorIdLast(anchorId))
                .orElse(new CrmAnchorOperate());

        // 主播信息表更新运营id
        CrmAnchor anchor = anchorService.getById(anchorId);
        anchor.setOperateId(convertToLong(operateId));
        anchorService.updateById(anchor);

        if (crmAnchorOperate.getId() != null) {
            crmAnchorOperate.setLoseTime(LocalDateTime.now());
            anchorOperateService.updateById(crmAnchorOperate);
            crmAnchorOperate.setLoseTime(null);
            crmAnchorOperate.setId(null);
        }
        // 新增运营主播关联表 脱手时间置null
        crmAnchorOperate.setOperateId(convertToLong(operateId));
        crmAnchorOperate.setAnchorId(convertToLong(anchorId));
        crmAnchorOperate.setJoinTime(LocalDateTime.now());
        super.sendBusTopic = anchorOperateService.save(crmAnchorOperate);
    }

    private void saveOrUpdateCustomerAscription(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        CrmCustomerAscription customerAscription = new CrmCustomerAscription();
        // 客户id
        customerAscription.setCustomerId(convertToLong(getAfterValueByFieldName("live_uid", fieldMap)));
        // 运营id
        customerAscription.setPyOperateId(convertToLong(getAfterValueByFieldName("admin_id", fieldMap)));
        // 保存或修改客户归属信息
        customerAscriptionService.saveOrUpdate(customerAscription);
    }

}
