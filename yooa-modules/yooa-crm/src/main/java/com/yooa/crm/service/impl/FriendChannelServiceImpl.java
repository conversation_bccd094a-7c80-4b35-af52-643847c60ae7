package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendChannel;
import com.yooa.crm.mapper.CrmFriendChannelMapper;
import com.yooa.crm.service.FriendChannelService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 好友渠道管理 - 服务实现层
 */
@AllArgsConstructor
@Service
public class FriendChannelServiceImpl extends ServiceImpl<CrmFriendChannelMapper, CrmFriendChannel> implements FriendChannelService {


}
