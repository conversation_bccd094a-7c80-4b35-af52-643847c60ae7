package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerRefund;
import com.yooa.crm.api.domain.vo.CustomerRefundRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/4 15:55
 * @Description:
 */
public interface CrmAnchorRefundMapper extends BaseMapper<CrmCustomerRefund> {
    List<CustomerRefundRecordVo> getRefundRecord(Page<CustomerRefundRecordVo> page, @Param("anchorId") Long anchorId);

    List<CustomerRefundRecordVo> getRefundCustomerIds(Page<CustomerRefundRecordVo> page, @Param("anchorId") Long anchorId);
}
