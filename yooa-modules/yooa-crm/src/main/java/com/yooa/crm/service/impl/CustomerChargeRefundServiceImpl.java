package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerChargeRefund;
import com.yooa.crm.mapper.CrmCustomerChargeRefundMapper;
import com.yooa.crm.service.CustomerChargeRefundService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/5 13:32
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class CustomerChargeRefundServiceImpl extends ServiceImpl<CrmCustomerChargeRefundMapper, CrmCustomerChargeRefund>
        implements CustomerChargeRefundService {
}
