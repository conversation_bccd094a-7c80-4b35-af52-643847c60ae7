package com.yooa.crm.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmFriendConfirm;
import com.yooa.crm.api.domain.query.FriendConfirmQuery;
import com.yooa.crm.api.domain.vo.FriendConfirmVo;

import java.time.LocalDate;
import java.util.List;

/**
 * 好友 - 服务层
 */
public interface FriendConfirmService extends IService<CrmFriendConfirm> {

    /**
     * 好友确认列表 - 推广
     */
    List<FriendConfirmVo> listByExtend(Page<FriendConfirmVo> page, FriendConfirmQuery query);

    /**
     * 好友确认列表 - 投放
     */
    List<FriendConfirmVo> listByPitcher(Page<FriendConfirmVo> page, FriendConfirmQuery query);

    /**
     * 获取好友确认信息
     */
    FriendConfirmVo getByConfirmId(Long confirmId);

    /**
     * 添加好友确认
     */
    int confirmSave(List<CrmFriendConfirm> confirmList);

    /**
     * 重新提交
     */
    int resubmit(CrmFriendConfirm friendConfirm);

    /**
     * 撤销申请
     */
    int revoke(Long confirmId);

    /**
     * 确认申请
     */
    int confirm(Long confirmId, String remark);

    /**
     * 驳回申请
     */
    int reject(Long confirmId, String remark);

    /**
     * 检查是否唯一
     */
    boolean checkFriendConfirmUnique(LocalDate friendDate, Long pitcherId, Long extendId, String publicType, Long mainChannelId, Long subChannelId, String sex, String language);

}
