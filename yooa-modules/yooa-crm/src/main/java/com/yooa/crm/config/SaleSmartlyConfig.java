package com.yooa.crm.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * SaleSmartly 配置信息
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "sale-smartly")
public class SaleSmartlyConfig {

    private Map<String, config> configs = new HashMap<>();

    @Getter
    @Setter
    public static class config {
        /**
         * 项目id
         */
        private String projectId;

        /**
         * API密钥
         */
        private String apiToken;
        /**
         * WebHook密钥
         */
        private String webHookToken;

        /**
         * 渠道 TK/FB
         */
        private String channel;
    }

}
