package com.yooa.crm.producer;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/21 15:03
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class KafkaMessageProducer {

    private final KafkaTemplate<String,String> kafkaTemplate;

    /**
     * 消息订阅消息拆分 订阅pd消息的主题负责数据库操作 业务处理逻辑在业务逻辑主题中进行处理
     * 订阅pd主题消费后 发送消息给下游主题 下游主题进行逻辑处理
     * @param topic 主题
     * @param message 消息
     */
    public void sendMessage(String topic, String message) {
        kafkaTemplate.send(topic, message);
    }
}
