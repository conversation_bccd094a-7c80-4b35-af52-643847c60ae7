package com.yooa.crm.listen.dts;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yooa.common.core.constant.KafkaBusinessTopicConstants;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.CrmCustomerReward;
import com.yooa.crm.mapper.CrmCustomerJoinAnchorMapper;
import com.yooa.crm.mapper.CrmCustomerMapper;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.CustomerRewardService;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class UsersCoinrecordListener extends AbstractBaseListener {

    private final CustomerRewardService customerRewardService;

    private final CrmCustomerJoinAnchorMapper customerJoinAnchorMapper;

    private final CrmCustomerMapper crmCustomerMapper;

    private final AnchorService anchorService;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_COINRECORD;
    }

    @Override
    public String getBusTopic() {
        return KafkaBusinessTopicConstants.BUS_CMF_USERS_COINRECORD;
    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();

    }


    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of(
                "id", "type", "action", "uid",
                "touid", "giftid", "dynamic_id",
                "totalcoin", "totalyuanbao",
                "showid", "addtime", "admin_id"
        );
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerReward(fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerReward(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void saveOrUpdateCustomerReward(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        String id = getAfterValueByFieldName("id", fieldMap);
        // 收支类型 "expend"=>消费,"income"=>收入
        String type = getAfterValueByFieldName("type", fieldMap);
        // 'fans_pack_send'=>'粉絲團包裹贈送禮物',
        String action = getAfterValueByFieldName("action", fieldMap);
        // 用户ID
        String customerId = getAfterValueByFieldName("uid", fieldMap);
        // 主播ID
        String anchorId = getAfterValueByFieldName("touid", fieldMap);
        // 行为对应ID
        String giftId = getAfterValueByFieldName("giftid", fieldMap);
        // 社区动态礼物消费 社区动态的ID
        String dynamicId = getAfterValueByFieldName("dynamic_id", fieldMap);
        // 总钻石价
        String totalcoin = getAfterValueByFieldName("totalcoin", fieldMap);
        // 总元宝价
        String totalyuanbao = getAfterValueByFieldName("totalyuanbao", fieldMap);
        // 直播标识
        String showId = getAfterValueByFieldName("showid", fieldMap);
        // 打赏时间
        String addtime = getAfterValueByFieldName("addtime", fieldMap);
        // 运营id
        String operateId = getAfterValueByFieldName("admin_id", fieldMap);

        // 客户表type = b 的不存打赏表 无用数据
        Long count = crmCustomerMapper.selectCount(new LambdaQueryWrapper<CrmCustomer>()
                .eq(CrmCustomer::getCustomerId, convertToLong(customerId))
                .eq(CrmCustomer::getType, "B"));
        if (count > 0) {
            return;
        }

        // 获取打赏金额
        BigDecimal totalAmount = getRewardMoney(totalcoin, totalyuanbao);
        // 只计算钻石元宝的金额  不管打赏金币的  钻石元宝累加金额为0 则是打赏金币的数据
        if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        // 业务逻辑处理
        handlerCdcUsersCoinrecord(id, type, action, customerId, anchorId, giftId, dynamicId, totalAmount,
                showId, addtime, operateId);
    }

    private void handlerCdcUsersCoinrecord(String id, String type, String action, String customerId, String anchorId,
            String giftId, String dynamicId, BigDecimal totalAmount, String showId,
            String addtime, String operateId) {
        CrmCustomerReward crmCustomerReward = new CrmCustomerReward();

        crmCustomerReward.setId(convertToLong(id));
        // 收支类型 "expend"=>消费,"income"=>收入
        crmCustomerReward.setType(type);
        // 收支行为
        crmCustomerReward.setAction(action);
        // 用户ID
        crmCustomerReward.setCustomerId(convertToLong(customerId));
        // 消费对象ID
        crmCustomerReward.setAnchorId(convertToLong(anchorId));
        // 行为对应ID
        crmCustomerReward.setGiftId(convertToLong(giftId));
        // 社区动态礼物消费 社区动态的ID
        crmCustomerReward.setDynamicId(convertToLong(dynamicId));
        // 打赏金额
        crmCustomerReward.setTotalAmount(totalAmount);
        // 直播标识
        crmCustomerReward.setShowId(convertToLong(showId));
        // 打赏时间
        crmCustomerReward.setAddTime(LocalDateUtil.epochSecondToLocalDateTime(addtime));
        // 运营id
        crmCustomerReward.setOperateId(convertToLong(operateId));
        // 插入更新数据
        super.sendBusTopic = customerRewardService.saveOrUpdate(crmCustomerReward);
        if (!sendBusTopic) {
            throw new ServiceException("UsersCoinrecordListener saveOrUpdate failed");
        }

        // 更新主播表打赏记录
        //   updateAnchorReward(convertToLong(anchorId));

        // 更新一交表打赏金额
        //   updateCustomerJoinAnchorAmt(convertToLong(anchorId),totalAmount);

    }

    /**
     * 更新一交表打赏金额
     */
//    private void updateCustomerJoinAnchorAmt(Long anchorId,  BigDecimal totalAmount) {
//        customerJoinAnchorMapper.updateCustomerJoinAnchorAmt(anchorId,totalAmount);
//
//    }

    /**
     * 打赏金额
     * (钻石 / 100) + 元宝
     */
    public BigDecimal getRewardMoney(String totalcoin, String totalyuanbao) {
        totalcoin = StrUtil.isNotBlank(totalcoin) ? totalcoin : "0";
        totalyuanbao = StrUtil.isNotBlank(totalyuanbao) ? totalyuanbao : "0";
        return NumberUtil.div(new BigDecimal(totalcoin), new BigDecimal("100"), 2, RoundingMode.HALF_UP)
                .add(new BigDecimal(totalyuanbao));
    }

    /**
     * 主播账号表更新客户打赏
     *
     * @param anchorId 锚id
     */
//    private void updateAnchorReward(Long anchorId) {
//        if (Objects.isNull(anchorId)) {
//            return;
//        }
//        // 查询客户打赏汇总
//        CustomerRewardDto customerRewardDto = customerRewardService.getAnchorRewardMoneyRecord(anchorId);
//
//        // 设置客户打赏
//        CrmAnchor crmAnchor = new CrmAnchor();
//        crmAnchor.setAnchorId(Convert.toLong(anchorId));
//        crmAnchor.setTodayRewardDate(LocalDateTime.now());
//        crmAnchor.setTodayRewardMoney(customerRewardDto.getTodayTotalReward());
//        crmAnchor.setMonthRewardMoney(customerRewardDto.getMonthTotalReward());
//        crmAnchor.setTotalRewardMoney(customerRewardDto.getTotalReward());
//
//        anchorService.updateById(crmAnchor);
//    }

}