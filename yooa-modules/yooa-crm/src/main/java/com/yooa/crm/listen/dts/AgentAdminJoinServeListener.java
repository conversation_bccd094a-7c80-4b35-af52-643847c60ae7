package com.yooa.crm.listen.dts;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.crm.api.domain.CrmCustomerJoinServe;
import com.yooa.crm.service.CustomerJoinServeService;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;


@Service
@RequiredArgsConstructor
public class AgentAdminJoinServeListener extends AbstractBaseListener {

    private final CustomerJoinServeService customerJoinServeService;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_AGENT_ADMIN_JOIN_SERVE;
    }
//
//    @Override
//    public String getBusTopic() {
//        return KafkaBusinessTopicConstants.BUS_CMF_AGENT_ADMIN_JOIN_SERVE;
//    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();
    }


    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("id", "uid", "serveid", "adminid", "state", "addtime", "uptime");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerJoinServe(data, fieldMap);

    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerJoinServe(data, fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        String id = getAfterValueByFieldName("id", fieldMap);
        customerJoinServeService.removeById(id);
    }

    private void saveOrUpdateCustomerJoinServe(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        CrmCustomerJoinServe crmCustomerJoinServe = new CrmCustomerJoinServe();
        crmCustomerJoinServe.setId(convertToLong(getAfterValueByFieldName("id", fieldMap)));
        // 客户id
        crmCustomerJoinServe.setCustomerId(convertToLong(getAfterValueByFieldName("uid", fieldMap)));
        // PD推广id
        crmCustomerJoinServe.setExtendId(convertToLong(getAfterValueByFieldName("adminid", fieldMap)));
        // PD客服id
        crmCustomerJoinServe.setServeId(convertToLong(getAfterValueByFieldName("serveid", fieldMap)));
        // 交接状态（0待交接 1已交接 2拒绝交接）
        crmCustomerJoinServe.setStatus(getAfterValueByFieldName("state", fieldMap));
        // 交接时间
        crmCustomerJoinServe.setHandoverTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("addtime", fieldMap)));
        // 接收时间
        crmCustomerJoinServe.setReceiveTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("uptime", fieldMap)));
        // 旧接收时间后续会舍弃
        crmCustomerJoinServe.setJoinTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("uptime", fieldMap)));
        // 更新或新增数据
        super.sendBusTopic = customerJoinServeService.saveOrUpdate(crmCustomerJoinServe);
        if (!sendBusTopic) {
            throw new ServiceException("AgentAdminJoinServeListener saverOrUpdate failed");
        }
    }
}
