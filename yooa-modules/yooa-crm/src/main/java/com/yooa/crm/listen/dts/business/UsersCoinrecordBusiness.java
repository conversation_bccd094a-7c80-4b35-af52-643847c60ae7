package com.yooa.crm.listen.dts.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yooa.common.core.constant.KafkaBusinessTopicConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.CrmCustomerReward;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.dto.CustomerRewardDto;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.mapper.CrmCustomerJoinAnchorMapper;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.CustomerRewardService;
import com.yooa.crm.service.FriendService;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UsersCoinrecordBusiness extends AbstractBaseBusiness {

    private final CrmCustomerFriendMapper crmCustomerFriendMapper;

    private final RemoteVermicelliService remoteVermicelliService;
    private final FriendService friendService;
    private final RemoteUserService remoteUserService;
    private final CustomerRewardService customerRewardService;

    private final CrmCustomerJoinAnchorMapper customerJoinAnchorMapper;

    private final AnchorService anchorService;

    @Override
    public String getTopic() {
        return KafkaBusinessTopicConstants.BUS_CMF_USERS_COINRECORD;
    }

    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of(
                "id", "uid", "touid", "totalcoin", "totalyuanbao", "admin_id"
        );
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void handleBusinessLogic(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        String id = getAfterValueByFieldName("id", fieldMap);
        String anchorId = getAfterValueByFieldName("touid", fieldMap);
        String customerId = getAfterValueByFieldName("uid", fieldMap);
        String operateId = getAfterValueByFieldName("admin_id", fieldMap);
        try {
            long startTime = System.currentTimeMillis();
            // 更新客户打赏金额
            updateAnchorReward(convertToLong(anchorId));

            // 总钻石价
            String totalcoin = getAfterValueByFieldName("totalcoin", fieldMap);
            // 总元宝价
            String totalyuanbao = getAfterValueByFieldName("totalyuanbao", fieldMap);

            // 获取打赏金额
            BigDecimal totalAmount = getRewardMoney(totalcoin, totalyuanbao);
            // 更新一交表打赏金额
            updateCustomerJoinAnchorAmt(convertToLong(anchorId), convertToLong(customerId), convertToLong(operateId), totalAmount);
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            System.out.println("-------执行运营业务时间为：" + executionTime + "毫秒---------");
        } catch (Exception e) {
            log.error("anchorId: [{}] 更新客户打赏失败 errMsg: [{}]", anchorId, e.getMessage());
        }


        CrmCustomerReward crmCustomerReward = customerRewardService.getById(convertToLong(id));
        // 不管新增还是修改的数据,需要满足两个条件
        // 1.客户在绑好友
        // 2.运营ID不等于0
        if (ObjUtil.isNotEmpty(crmCustomerReward) &&
                ObjUtil.isNotEmpty(crmCustomerReward.getOperateId()) && crmCustomerReward.getOperateId() != 0) {
            // 判断新增运营粉丝登记
            judgmentOperateVermicelliAdd(crmCustomerReward);
        }

    }

    public BigDecimal getRewardMoney(String totalcoin, String totalyuanbao) {
        totalcoin = StrUtil.isNotBlank(totalcoin) ? totalcoin : "0";
        totalyuanbao = StrUtil.isNotBlank(totalyuanbao) ? totalyuanbao : "0";
        return NumberUtil.div(new BigDecimal(totalcoin), new BigDecimal("100"), 2, RoundingMode.HALF_UP)
                .add(new BigDecimal(totalyuanbao));
    }

    /**
     * 更新一交表打赏金额
     */
    private void updateCustomerJoinAnchorAmt(Long anchorId, Long customerId, Long operateId, BigDecimal totalAmount) {
        customerJoinAnchorMapper.updateCustomerJoinAnchorAmt(anchorId, customerId, operateId, totalAmount);

    }

    /**
     * 主播账号表更新客户打赏
     *
     * @param anchorId 锚id
     */
    private void updateAnchorReward(Long anchorId) {
        if (Objects.isNull(anchorId)) {
            return;
        }
        // 查询客户打赏汇总
        CustomerRewardDto customerRewardDto = customerRewardService.getAnchorRewardMoneyRecord(anchorId);
        if (customerRewardDto != null) {
            // 设置客户打赏
            CrmAnchor crmAnchor = new CrmAnchor();
            crmAnchor.setAnchorId(Convert.toLong(anchorId));
            crmAnchor.setTodayRewardDate(LocalDateTime.now());
            crmAnchor.setTodayRewardMoney(customerRewardDto.getTodayTotalReward());
            crmAnchor.setMonthRewardMoney(customerRewardDto.getMonthTotalReward());
            crmAnchor.setTotalRewardMoney(customerRewardDto.getTotalReward());
            anchorService.updateById(crmAnchor);
        }
    }

    /**
     * 判断运营粉丝登记(新增)
     */
    private void judgmentOperateVermicelliAdd(CrmCustomerReward crmCustomerReward) {
        // 查出此客户的关联好友
        CrmCustomerFriend friendCustomer = crmCustomerFriendMapper.selectOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, crmCustomerReward.getCustomerId())
                .eq(CrmCustomerFriend::getStatus, 0)
                .apply("end_time IS NULL")
                .last("limit 1"));

        if (ObjUtil.isNotNull(friendCustomer)) {        // 客户没有绑定好友不进行粉丝登记
            // 查询好友下绑定的客户
            List<CrmCustomerFriend> customerFriends = crmCustomerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .in(CrmCustomerFriend::getFriendId, friendCustomer.getFriendId())
                    .eq(CrmCustomerFriend::getStatus, 0)
                    .apply("end_time IS NULL"));
            // 好友
            CrmFriend friend = friendService.getById(friendCustomer.getFriendId());

            List<SysUserVo> operateUserList = remoteUserService.getUserList(UserQuery.builder()
                    .pdUserId(CollUtil.newArrayList(crmCustomerReward.getOperateId())).build(), SecurityConstants.INNER).getData();

            SysUserVo operateUser = new SysUserVo();
            if (CollUtil.isNotEmpty(operateUserList) && operateUserList.size() > 0) {
                if (operateUserList.size() > 1) {
                    throw new ServiceException("打赏表业务处理异常:[" + crmCustomerReward.getOperateId() + "]存在多个OA用户绑定!");
                }
                operateUser = operateUserList.get(0);
            }

            if (ObjUtil.isNotNull(operateUser) && CollUtil.isNotEmpty(operateUser.getPdUserId())) {
                List<CrmCustomerReward> crmCustomerRewardList = customerRewardService.list(new LambdaQueryWrapper<CrmCustomerReward>()
                        .in(CrmCustomerReward::getCustomerId, customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList())
                        .eq(CrmCustomerReward::getOperateId, crmCustomerReward.getOperateId())
                        .orderByDesc(CrmCustomerReward::getAddTime)); // 打赏表排序

                // 运营粉丝登记模版
                OperateVermicelli vermicelli = new OperateVermicelli();
                vermicelli.setPyOperateId(crmCustomerReward.getOperateId());
                vermicelli.setFriendId(friendCustomer.getFriendId());
                vermicelli.setCustomerIds(customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList()
                        .stream().map(String::valueOf).collect(Collectors.joining(",")));
                vermicelli.setAnchorId(crmCustomerReward.getAnchorId());
                vermicelli.setOperateId(operateUser.getUserId());
                vermicelli.setOperateDeptId(operateUser.getDeptId());
                vermicelli.setCreateTime(LocalDateTime.now());
                vermicelli.setRemark("打赏表产生粉丝登记");
                vermicelli.setCreateBy(1L);

                // 好友已录入的粉丝登记
                List<OperateVermicelli> operateVermicelliList = remoteVermicelliService.selOperateVermicelliList(
                        CollUtil.newArrayList(operateUser.getUserId()),
                        null,
                        null,
                        CollUtil.newArrayList(friendCustomer.getFriendId()),
                        null,
                        null,
                        SecurityConstants.INNER).getData();

                List<String> yearMonthList = crmCustomerRewardList.stream()
                        .map(CrmCustomerReward::getAddTime)
                        .map(d -> d.getYear() + "-" + String.format("%02d", d.getMonthValue()))
                        .collect(Collectors.toCollection(TreeSet::new)) // 使用 TreeSet 去重并排序
                        .stream()
                        .collect(Collectors.toList());

                boolean blsc = operateVermicelliList.stream().count() == 0;
                boolean blyw100 = operateVermicelliList.stream().filter(o -> o.getFansType() == 0).count() == 0;
                boolean bl200 = operateVermicelliList.stream().filter(o -> o.getFansType() == 1).count() == 0;
                boolean bl500 = operateVermicelliList.stream().filter(o -> o.getFansType() == 2).count() == 0;

                if (blsc) {
                    vermicelli.setFansType(6);
                    vermicelli.setRecordDate(crmCustomerRewardList.stream().map(CrmCustomerReward::getAddTime).min(LocalDateTime::compareTo).get().toLocalDate());
                    remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                }

                if (blyw100 && ObjUtil.isNotNull(friend.getFansType()) && friend.getFansType().equals("3")) {       // 英文好友才能创建此类型
                    // 按天分组并累加 totalAmount
                    Map<LocalDate, BigDecimal> dailyRewarMoneyMap = crmCustomerRewardList.stream()
                            .collect(Collectors.groupingBy(
                                    reward -> reward.getAddTime().toLocalDate(), // 按天分组
                                    Collectors.reducing(BigDecimal.ZERO, reward -> reward.getTotalAmount(), BigDecimal::add) // 累加 totalAmount
                            ));

                    LocalDate firstGreaterThanyw100 = dailyRewarMoneyMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())         // 排序
                            .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(100)) >= 0)
                            .map(Map.Entry::getKey)
                            .findFirst()
                            .orElse(null);

                    if (ObjUtil.isNotNull(firstGreaterThanyw100)) {
                        vermicelli.setFansType(0);
                        vermicelli.setRecordDate(firstGreaterThanyw100);
                        remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                    }
                }

                if (bl200 || bl500) {
                    // 按天分组并累加 totalAmount
                    Map<LocalDate, BigDecimal> dailyRewarMoneyMap = crmCustomerRewardList.stream()
                            .collect(Collectors.groupingBy(
                                    reward -> reward.getAddTime().toLocalDate(), // 按天分组
                                    Collectors.reducing(BigDecimal.ZERO, reward -> reward.getTotalAmount(), BigDecimal::add) // 累加 totalAmount
                            ));
                    if (bl200) {
                        LocalDate firstGreaterThan200 = dailyRewarMoneyMap.entrySet().stream()
                                .sorted(Map.Entry.comparingByKey())         // 排序
                                .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                .map(Map.Entry::getKey)
                                .findFirst()
                                .orElse(null);

                        if (ObjUtil.isNotNull(firstGreaterThan200)) {
                            vermicelli.setFansType(1);
                            vermicelli.setRecordDate(firstGreaterThan200);
                            remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                    }
                    if (bl500) {
                        LocalDate firstGreaterThan500 = dailyRewarMoneyMap.entrySet().stream()
                                .sorted(Map.Entry.comparingByKey())         // 排序
                                .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                .map(Map.Entry::getKey)
                                .findFirst()
                                .orElse(null);

                        if (ObjUtil.isNotNull(firstGreaterThan500)) {
                            vermicelli.setFansType(2);
                            vermicelli.setRecordDate(firstGreaterThan500);
                            remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                    }
                }

                Long num5k = operateVermicelliList.stream().filter(v -> v.getFansType() == 3
                        && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
                Long num5w = operateVermicelliList.stream().filter(v -> v.getFansType() == 4
                        && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
                Long num10w = operateVermicelliList.stream().filter(v -> v.getFansType() == 5
                        && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
                boolean bl5k = num5k.intValue() != yearMonthList.size();
                boolean bl5w = num5w.intValue() != yearMonthList.size();
                boolean bl10w = num10w.intValue() != yearMonthList.size();

                if (bl5k || bl5w || bl10w) {
                    boolean bl5kOlb = operateVermicelliList.stream().filter(o -> o.getFansType() == 3).count() == 0;
                    boolean bl5wOlb = operateVermicelliList.stream().filter(o -> o.getFansType() == 4).count() == 0;
                    boolean bl10wOlb = operateVermicelliList.stream().filter(o -> o.getFansType() == 5).count() == 0;

                    for (String ym : yearMonthList) {
                        boolean monthBl5k = true;
                        boolean monthBl5w = true;
                        boolean monthBl10w = true;
                        if (CollUtil.isNotEmpty(operateVermicelliList)) {
                            monthBl5k = operateVermicelliList.stream().filter(v -> v.getFansType() == 3
                                    && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                            monthBl5w = operateVermicelliList.stream().filter(v -> v.getFansType() == 4
                                    && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                            monthBl10w = operateVermicelliList.stream().filter(v -> v.getFansType() == 5
                                    && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                        }

                        if (monthBl5k || monthBl5w || monthBl10w) {
                            BigDecimal monthUp = crmCustomerRewardList.stream().filter(o -> ym.equals(o.getAddTime().getYear() + "-" + String.format("%02d", o.getAddTime().getMonthValue())))
                                    .map(CrmCustomerReward::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的打赏数

                            if (monthBl5k && monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                                LocalDateTime dateTime5k = crmCustomerRewardList.stream()
                                        .filter(o -> ym.equals(o.getAddTime().getYear() + "-" + String.format("%02d", o.getAddTime().getMonthValue())))
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getTotalAmount());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getAddTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue();
                                vermicelli.setRecordDate(dateTime5k.toLocalDate());

                                // 新增5k记录
                                vermicelli.setFansType(3);
                                vermicelli.setStatus(bl5kOlb ? "0" : "1");
                                remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                                bl5kOlb = false;
                            }
                            if (monthBl5w && monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0) {
                                LocalDateTime dateTime5w = crmCustomerRewardList.stream()
                                        .filter(o -> ym.equals(o.getAddTime().getYear() + "-" + String.format("%02d", o.getAddTime().getMonthValue())))
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getTotalAmount());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getAddTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue();
                                vermicelli.setRecordDate(dateTime5w.toLocalDate());

                                // 新增5w记录
                                vermicelli.setFansType(4);
                                vermicelli.setStatus(bl5wOlb ? "0" : "1");
                                remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                                bl5wOlb = false;
                            }
                            if (monthBl10w && monthUp.compareTo(BigDecimal.valueOf(100000)) >= 0) {
                                LocalDateTime dateTime10w = crmCustomerRewardList.stream()
                                        .filter(o -> ym.equals(o.getAddTime().getYear() + "-" + String.format("%02d", o.getAddTime().getMonthValue())))
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getTotalAmount());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(100000)) >= 0) {  // 统计第一次累加到10万时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getAddTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue();
                                vermicelli.setRecordDate(dateTime10w.toLocalDate());

                                // 新增10w记录
                                vermicelli.setFansType(5);
                                vermicelli.setStatus(bl10wOlb ? "0" : "1");
                                remoteVermicelliService.addOperateVermicelli(vermicelli, SecurityConstants.INNER);
                                bl10wOlb = false;
                            }

                        }
                    }
                }
            }
        }
    }
}