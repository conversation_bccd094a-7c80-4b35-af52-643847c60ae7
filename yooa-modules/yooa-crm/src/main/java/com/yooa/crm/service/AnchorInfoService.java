package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.api.domain.query.AnchorInfoQuery;
import com.yooa.crm.api.domain.query.AnchorRosterQuery;
import com.yooa.crm.api.domain.vo.AnchorAccountMappingVo;
import com.yooa.crm.api.domain.vo.AnchorInfoVo;
import com.yooa.crm.api.domain.vo.AnchorRosterVo;

import java.util.List;

/**
 * 主播信息 - 业务层
 */
public interface AnchorInfoService extends IService<CrmAnchorInfo> {

    /**
     * 主播信息
     */
    List<AnchorInfoVo> list(Page<AnchorInfoVo> page, AnchorInfoQuery query);

    /**
     * 获取主播信息详情
     */
    AnchorInfoVo getAnchorInfoById(Long anchorId);


    /**
     * 根据主播id获取所有账户
     */
    List<AnchorAccountMappingVo> accountListByAnchorId(Long anchorId);

    /**
     * 获取主播花名册
     */
    List<AnchorRosterVo> getAnchorRoster(Page page, AnchorRosterQuery query);

    /**
     * 获取主播面试表单
     */
    String getRegistrationFormUrl(Long anchorId);
}
