package com.yooa.crm.listen.dts;

import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.crm.api.domain.CrmCustomerViewLiveTime;
import com.yooa.crm.service.CustomerViewLiveTimeService;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertTo;
import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class UsersLiveTimeListener extends AbstractBaseListener {

    private final CustomerViewLiveTimeService customerViewLiveTimeService;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_NEW_LIVETIME;
    }

//    @Override
//    public String getBusTopic() {
//        return KafkaBusinessTopicConstants.BUS_CMF_USERS_LIVETIME;
//    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();

    }

    @Override
    protected List<String> getFilterList() {
        return List.of("uid", "luid", "createtime", "day", "entertime");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveCustomerViewLiveTime(fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveCustomerViewLiveTime(fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void saveCustomerViewLiveTime(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        // 客户id
        String customerId = getAfterValueByFieldName("uid", fieldMap);
        // 主播id
        String anchorId = getAfterValueByFieldName("luid", fieldMap);
        // 客户进入时间
        String createTime = getAfterValueByFieldName("createtime", fieldMap);
        // 观看日期
        String viewDate = getAfterValueByFieldName("day", fieldMap);
        // 观看时间 (秒)
        String enterTime = getAfterValueByFieldName("entertime", fieldMap);

        // 保存用户观看直播时间表
        CrmCustomerViewLiveTime customerViewLiveTime = CrmCustomerViewLiveTime.builder()
                .anchorId(convertToLong(anchorId))
                .customerId(convertToLong(customerId))
                .viewTime(LocalDateUtil.epochSecondToLocalDateTime(createTime))
                .viewDate(LocalDate.parse(viewDate))
                .enterTime(convertTo(enterTime, Long.class))
                .build();
        customerViewLiveTimeService.save(customerViewLiveTime);
        super.sendBusTopic = true;
    }
}
