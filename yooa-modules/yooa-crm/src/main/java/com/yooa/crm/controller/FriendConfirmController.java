package com.yooa.crm.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.idempotent.annotation.Idempotent;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.CrmFriendConfirm;
import com.yooa.crm.api.domain.CrmFriendConfirmReview;
import com.yooa.crm.api.domain.dto.FriendConfirmEditDto;
import com.yooa.crm.api.domain.dto.FriendConfirmSaveDto;
import com.yooa.crm.api.domain.query.FriendConfirmQuery;
import com.yooa.crm.api.domain.vo.FriendConfirmVo;
import com.yooa.crm.service.FriendConfirmReviewService;
import com.yooa.crm.service.FriendConfirmService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.List;

/**
 * 好友确认 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/friend/confirm")
public class FriendConfirmController extends BaseController {

    private final FriendConfirmService friendConfirmService;

    private final FriendConfirmReviewService friendConfirmReviewService;

    /**
     * 好友确认列表 - 推广
     */
    @GetMapping("/extend/list")
    public AjaxResult listByExtend(Page<FriendConfirmVo> page, FriendConfirmQuery query) {
        if (!SecurityUtils.isAdmin()) {
            query.setExtendId(SecurityUtils.getUserId());
        }
        return success(page.setRecords(friendConfirmService.listByExtend(page, query)));
    }

    /**
     * 好友确认列表 - 投放
     */
    @GetMapping("/pitcher/list")
    public AjaxResult listByPitcher(Page<FriendConfirmVo> page, FriendConfirmQuery query) {
        return success(page.setRecords(friendConfirmService.listByPitcher(page, query)));
    }

    /**
     * 好友确认列表 - 投放
     */
    @GetMapping("/{confirmId}")
    public AjaxResult getByConfirmId(@PathVariable Long confirmId) {
        return success(friendConfirmService.getByConfirmId(confirmId));
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, FriendConfirmQuery query) {
        List<FriendConfirmVo> list = new ArrayList<>();
        if ("extend".equals(query.getModule())) {
            list = friendConfirmService.listByExtend(null, query);
        }
        else if ("pitcher".equals(query.getModule())) {
            list = friendConfirmService.listByPitcher(null, query);
        }
        ExcelUtil<FriendConfirmVo> util = new ExcelUtil<>(FriendConfirmVo.class);
        util.exportExcel(response, list, "好友数确认列表");
    }


    /**
     * 添加好友确认
     */
    @Log(title = "好友确认", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult save(@Valid @RequestBody List<FriendConfirmSaveDto> confirmList) {
        return success(friendConfirmService.confirmSave(BeanUtil.copyToList(confirmList, CrmFriendConfirm.class)));
    }

    /**
     * 编辑好友确认
     */
    @Log(title = "好友确认", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody FriendConfirmEditDto friendConfirmEditDto) {
        CrmFriendConfirm friendConfirm = friendConfirmService.getById(friendConfirmEditDto.getConfirmId());
        if ("1".equals(friendConfirm.getStatus()) || "2".equals(friendConfirm.getStatus())) {
            return warn("该数据已提交或已确认，不允许编辑");
        }
        return success(friendConfirmService.updateById(friendConfirmEditDto));
    }

    @Valid
    @Idempotent
    @Log(title = "好友确认", businessType = BusinessType.REVIEW)
    @PutMapping("/resubmit/{confirmId}")
    public AjaxResult resubmit(@PathVariable Long confirmId, @RequestParam(required = false) String remark) {
        CrmFriendConfirm friendConfirm = friendConfirmService.getById(confirmId);
        if ("1".equals(friendConfirm.getStatus()) || "2".equals(friendConfirm.getStatus())) {
            return warn("该数据已提交或已确认，请勿重复操作");
        }
        friendConfirm.setRemark(remark);
        return success(friendConfirmService.resubmit(friendConfirm));
    }

    /**
     * 撤销申请
     */
    @Idempotent
    @Log(title = "好友确认", businessType = BusinessType.REVIEW)
    @PutMapping("/revoke/{confirmId}")
    public AjaxResult revoke(@PathVariable Long confirmId) {
        CrmFriendConfirm friendConfirm = friendConfirmService.getById(confirmId);
        if ("4".equals(friendConfirm.getStatus())) {
            return warn("该数据已撤销，请勿重复操作");
        }
        if ("2".equals(friendConfirm.getStatus()) || "3".equals(friendConfirm.getStatus())) {
            return warn("该数据已确认或已驳回，请刷新后重试");
        }
        return success(friendConfirmService.revoke(confirmId));
    }

    /**
     * 确认申请
     */
    @Valid
    @Idempotent
    @Log(title = "好友确认", businessType = BusinessType.REVIEW)
    @PutMapping("/confirm/{confirmId}")
    public AjaxResult confirm(@PathVariable Long confirmId, @NotBlank(message = "请填写确认理由") String remark) {
        CrmFriendConfirm friendConfirm = friendConfirmService.getById(confirmId);
        if ("2".equals(friendConfirm.getStatus())) {
            return warn("该数据已确认，请勿重复操作");
        }
        if ("3".equals(friendConfirm.getStatus()) || "4".equals(friendConfirm.getStatus())) {
            return warn("该数据已撤销或已驳回，请刷新后重试");
        }
        return success(friendConfirmService.confirm(confirmId, remark));
    }

    /**
     * 驳回申请
     */
    @Valid
    @Idempotent
    @Log(title = "好友确认", businessType = BusinessType.REVIEW)
    @PutMapping("/reject/{confirmId}")
    public AjaxResult reject(@PathVariable Long confirmId, @NotBlank(message = "请填写驳回原因") String remark) {
        CrmFriendConfirm friendConfirm = friendConfirmService.getById(confirmId);
        if ("3".equals(friendConfirm.getStatus())) {
            return warn("该数据已驳回，请勿重复操作");
        }
        if ("4".equals(friendConfirm.getStatus())) {
            return warn("该数据已撤销，请刷新后重试");
        }
        // 验证驳回的申请单是否满足驳回条件
        // step1：当月的可无条件驳回
        // step2：上月的只能在5号以前才能驳回
        // step3：上月以前的不允许驳回
        // 需要兼容一个条件，之前没有加时间限制导致有些数据是在当前时间之后的，需要兼容允许驳回
        LocalDate nowDate = LocalDate.now();
        LocalDate friendDate = friendConfirm.getFriendDate();
        if (nowDate.getMonthValue() != friendDate.getMonthValue()) {
            Period period = LocalDateUtil.betweenPeriod(friendDate.withDayOfMonth(1), nowDate.withDayOfMonth(1));
            if (nowDate.getDayOfMonth() < 5 && period.getMonths() > 1) {
                return warn("该数据已结算，不允许驳回");
            }
            if (nowDate.getDayOfMonth() >= 5 && period.getMonths() == 1) {
                return warn("该数据已结算，不允许驳回");
            }
        }
        return success(friendConfirmService.reject(confirmId, remark));
    }

    /**
     * 审核列表
     */
    @GetMapping("/review-list/{confirmId}/{status}")
    public AjaxResult reviewList(@PathVariable Long confirmId, @PathVariable List<String> status) {
        List<CrmFriendConfirmReview> list = friendConfirmReviewService.list(
                Wrappers.<CrmFriendConfirmReview>lambdaQuery()
                        .eq(CrmFriendConfirmReview::getConfirmId, confirmId)
                        .in(CrmFriendConfirmReview::getStatus, status)
                        .isNotNull(CrmFriendConfirmReview::getRemark)
                        .orderByDesc(CrmFriendConfirmReview::getCreateTime));
        return success(list);
    }
}
