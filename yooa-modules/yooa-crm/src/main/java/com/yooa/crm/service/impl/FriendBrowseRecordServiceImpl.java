package com.yooa.crm.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendBrowseRecord;
import com.yooa.crm.api.domain.query.FriendBrowseRecordQuery;
import com.yooa.crm.api.domain.vo.FriendBrowseRecordVo;
import com.yooa.crm.mapper.CrmFriendBrowseRecordMapper;
import com.yooa.crm.service.FriendBrowseRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 好友浏览记录 - 服务实现层
 */
@AllArgsConstructor
@Service
public class FriendBrowseRecordServiceImpl extends ServiceImpl<CrmFriendBrowseRecordMapper, CrmFriendBrowseRecord> implements FriendBrowseRecordService {

    @Override
    public List<FriendBrowseRecordVo> list(FriendBrowseRecordQuery query) {
        return baseMapper.selectFriendBrowseRecordList(query);
    }
}




