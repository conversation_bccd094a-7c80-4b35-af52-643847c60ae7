package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.query.RoiQuery;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.service.RoiService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 投资回报模型 - 控制层
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/roi")
public class RoiController extends BaseController {

    private final RoiService roiService;

    /**
     * 投手角度投资回报模型统计 - 以推广分组形式汇总数据
     */
    @GetMapping("/pitcher/statistics-extend")
    public AjaxResult getPitcherRoiStatisticsGroupByExtend(@Valid RoiQuery query) {
        return success(roiService.getPitcherRoiStatisticsGroupByExtend(query));
    }

    /**
     * 投手角度投资回报模型 - 以推广分组形式汇总数据
     */
    @GetMapping("/pitcher/list-extend")
    public AjaxResult getPitcherRoiGroupByExtend(Page<ExtendRoiVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getPitcherRoiGroupByExtend(page, query)));
    }

    /**
     * 投手角度投资回报模型 - 以推广分组形式汇总数据
     */
    @PostMapping("/pitcher/export-extend")
    public void exportPitcherRoiGroupByExtend(HttpServletResponse response, @Valid RoiQuery query) {
        List<ExtendRoiVo> roiList = roiService.getPitcherRoiGroupByExtend(null, query);
        ExcelUtil<ExtendRoiVo> util = new ExcelUtil<>(ExtendRoiVo.class);
        util.exportExcel(response, roiList, "推广数据");
    }

    /**
     * 投手角度投资回报模型统计 - 以时间分组形式汇总数据
     */
    @GetMapping("/pitcher/statistics-date")
    public AjaxResult pitcherRoiStatisticsGroupByDate(@Valid RoiQuery query) {
        return success(roiService.getPitcherRoiStatisticsGroupByDate(query));
    }

    /**
     * 投手角度投资回报模型 - 以时间分组形式汇总数据
     */
    @GetMapping("/pitcher/list-date")
    public AjaxResult getPitcherRoiGroupByDate(Page<DateRoiVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getPitcherRoiGroupByDate(page, query)));
    }

    /**
     * 投手角度投资回报模型 - 以时间分组形式汇总数据
     */
    @PostMapping("/pitcher/export-date")
    public void exportPitcherRoiGroupByDate(HttpServletResponse response, @Valid RoiQuery query) {
        List<DateRoiVo> roiList = roiService.getPitcherRoiGroupByDate(null, query);
        ExcelUtil<DateRoiVo> util = new ExcelUtil<>(DateRoiVo.class);
        util.exportExcel(response, roiList, "投放数据");
    }

    /**
     * 投手角度投资回报模型统计 - 以投手分组形式汇总数据
     */
    @GetMapping("/pitcher/statistics-pitcher")
    public AjaxResult getPitcherRoiStatisticsGroupByPitcher(@Valid RoiQuery query) {
        return success(roiService.getPitcherRoiStatisticsGroupByPitcher(query));
    }

    /**
     * 投手角度投资回报模型 - 以投手分组形式汇总数据
     */
    @GetMapping("/pitcher/list-pitcher")
    public AjaxResult getPitcherRoiGroupByPitcher(Page<PitcherRoiVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getPitcherRoiGroupByPitcher(page, query)));
    }

    /**
     * 投手角度投资回报模型 - 以投手分组形式汇总数据
     */
    @PostMapping("/pitcher/export-pitcher")
    public void exportPitcherRoiGroupByPitcher(HttpServletResponse response, @Valid RoiQuery query) {
        List<PitcherRoiVo> roiList = roiService.getPitcherRoiGroupByPitcher(null, query);
        ExcelUtil<PitcherRoiVo> util = new ExcelUtil<>(PitcherRoiVo.class);
        util.exportExcel(response, roiList, "投手数据");
    }

    /**
     * 投手业绩统计
     */
    @GetMapping("/pitcher/statistics-performance")
    public AjaxResult getPitcherPerformanceStatistics(@Valid RoiQuery query) {
        return success(roiService.getPitcherPerformanceStatistics(query));
    }

    /**
     * 投手业绩
     */
    @GetMapping("/pitcher/list-performance")
    public AjaxResult getPitcherPerformance(Page<PitcherPerformanceVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getPitcherPerformance(page, query)));
    }

    /**
     * 投手业绩
     */
    @PostMapping("/pitcher/export-performance")
    public void exportPitcherPerformance(HttpServletResponse response, @Valid RoiQuery query) {
        List<PitcherPerformanceVo> roiList = roiService.getPitcherPerformance(null, query);
        ExcelUtil<PitcherPerformanceVo> util = new ExcelUtil<>(PitcherPerformanceVo.class);
        util.exportExcel(response, roiList, "投手业绩");
    }

    /**
     * 推广业绩
     */
    @GetMapping("/pitcher/statistics-extend-performance")
    public AjaxResult getExtendPerformanceStatistics(@Valid RoiQuery query) {
        return success(roiService.getExtendPerformanceStatistics(query));
    }

    /**
     * 推广业绩
     */
    @GetMapping("/pitcher/extend-performance")
    public AjaxResult getExtendPerformance(Page<ExtendPerformanceVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getExtendPerformance(page, query)));
    }

    /**
     * 推广业绩
     */
    @PostMapping("/pitcher/export-extend-performance")
    public void exportExtendPerformance(HttpServletResponse response, @Valid RoiQuery query) {
        List<ExtendPerformanceVo> roiList = roiService.getExtendPerformance(null, query);
        ExcelUtil<ExtendPerformanceVo> util = new ExcelUtil<>(ExtendPerformanceVo.class);
        util.exportExcel(response, roiList, "推广业绩");
    }

    /**
     * 推广角度投资回报模型 - 以推广分组形式汇总数据
     */
    @GetMapping("/extend/statistics-extend")
    public AjaxResult getExtendRoiStatisticsGroupByExtend(@Valid RoiQuery query) {
        return success(roiService.getExtendRoiStatisticsGroupByExtend(query));
    }

    /**
     * 推广角度投资回报模型 - 以推广分组形式汇总数据
     */
    @GetMapping("/extend/list-extend")
    public AjaxResult getExtendRoiGroupByExtend(Page<ExtendRoiVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getExtendRoiGroupByExtend(page, query)));
    }

    /**
     * 推广角度投资回报模型 - 以推广分组形式汇总数据
     */
    @PostMapping("/extend/export-extend")
    public void exportExtendRoiGroupByExtend(HttpServletResponse response, @Valid RoiQuery query) {
        List<ExtendRoiVo> roiList = roiService.getExtendRoiGroupByExtend(null, query);
        ExcelUtil<ExtendRoiVo> util = new ExcelUtil<>(ExtendRoiVo.class);
        util.exportExcel(response, roiList, "推广数据");
    }

    /**
     * 推广角度投资回报模型 - 以时间分组形式汇总数据
     */
    @GetMapping("/extend/list-date")
    public AjaxResult getExtendRoiGroupByDate(Page<DateRoiVo> page, @Valid RoiQuery query) {
        return success(page.setRecords(roiService.getExtendRoiGroupByDate(page, query)));
    }

    /**
     * 推广角度投资回报模型 - 以时间分组形式汇总数据
     */
    @GetMapping("/extend/statistics-date")
    public AjaxResult getExtendRoiStatisticsGroupByDate(@Valid RoiQuery query) {
        return success(roiService.getExtendRoiStatisticsGroupByDate(query));
    }

    /**
     * 推广角度投资回报模型 - 以时间分组形式汇总数据
     */
    @PostMapping("/extend/export-date")
    public void exportExtendRoiGroupByDate(HttpServletResponse response, @Valid RoiQuery query) {
        query.setExtendId(SecurityUtils.getUserId());
        List<DateRoiVo> roiList = roiService.getExtendRoiGroupByDate(null, query);
        ExcelUtil<DateRoiVo> util = new ExcelUtil<>(DateRoiVo.class);
        util.exportExcel(response, roiList, "投放数据");
    }

}
