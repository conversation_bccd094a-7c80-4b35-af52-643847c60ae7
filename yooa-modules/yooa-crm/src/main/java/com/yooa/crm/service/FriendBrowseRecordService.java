package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmFriendBrowseRecord;
import com.yooa.crm.api.domain.query.FriendBrowseRecordQuery;
import com.yooa.crm.api.domain.vo.FriendBrowseRecordVo;

import java.util.List;

/**
 * 好友浏览记录 - 服务层
 */
public interface FriendBrowseRecordService extends IService<CrmFriendBrowseRecord> {

    /**
     * 获取好友浏览记录列表
     */
    List<FriendBrowseRecordVo> list(FriendBrowseRecordQuery query);
    
}
