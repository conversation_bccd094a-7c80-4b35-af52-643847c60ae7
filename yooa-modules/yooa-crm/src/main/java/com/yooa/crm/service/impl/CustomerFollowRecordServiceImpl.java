package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerFollowRecord;
import com.yooa.crm.api.domain.query.CustomerFollowRecordQuery;
import com.yooa.crm.api.domain.vo.CustomerFollowRecordVo;
import com.yooa.crm.mapper.CrmCustomerFollowRecordMapper;
import com.yooa.crm.service.CustomerFollowRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户跟踪记录 - 服务层实现
 */
@AllArgsConstructor
@Service
public class CustomerFollowRecordServiceImpl extends ServiceImpl<CrmCustomerFollowRecordMapper, CrmCustomerFollowRecord> implements CustomerFollowRecordService {

    @Override
    public List<CustomerFollowRecordVo> list(CustomerFollowRecordQuery query) {
        return baseMapper.selectCustomerFollowRecordList(query);
    }

}
