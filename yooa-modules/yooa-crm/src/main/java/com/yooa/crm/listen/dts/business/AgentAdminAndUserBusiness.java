package com.yooa.crm.listen.dts.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yooa.cmf.api.RemoteCmfUserService;
import com.yooa.cmf.api.domain.CmfUsers;
import com.yooa.common.core.constant.KafkaBusinessTopicConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.crm.api.domain.CrmCustomer;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.mapper.CrmFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import com.yooa.crm.service.CustomerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgentAdminAndUserBusiness extends AbstractBaseBusiness {

    private final CustomerFriendService customerFriendService;

    private final CustomerService customerService;

    private final RemoteCmfUserService remoteCmfUserService;

    private final CrmFriendMapper friendMapper;

    @Override
    public String getTopic() {
        return KafkaBusinessTopicConstants.BUS_CMF_AGENT_ADMIN_AND_USER;
    }

    @Override
    protected List<String> getFilterList() {
        return List.of("admin_id", "user_id");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap, 1);
        // handleBusinessLogicUUidDevice(fieldMap);    // 逻辑处理设备编码
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap, 2);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void handleBusinessLogic(Map<String, SubscribeConvertDbData.Field> fieldMap, Integer operatorType) {
        // 原推广归属
        String beforeExtendId = "0";
        if (operatorType == 2) {    // 新增时没有原推广归属
            beforeExtendId = getBeforeValueByFieldName("admin_id", fieldMap);
        }

        // 现推广归属
        String afterExtendId = getAfterValueByFieldName("admin_id", fieldMap);
        // 客户id
        String customerId = getAfterValueByFieldName("user_id", fieldMap);
        // 推广归属改变,并且原归属不为0
        if (!StrUtil.equals(afterExtendId, beforeExtendId) && !beforeExtendId.equals("0")) {
            List<CrmCustomerFriend> customerFriendList = customerFriendService.list(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .eq(CrmCustomerFriend::getCustomerId, customerId)
                    .eq(CrmCustomerFriend::getPyExtendId, beforeExtendId)
                    .eq(CrmCustomerFriend::getStatus, 0));

            // 查询该客户id是否是公海领取的好友改绑(判断现归属是否等于好友领取表中好友的领取人id)  如果是则将好友表状态改成激活
            friendMapper.updateStatusByCustomerId(customerId, afterExtendId);

            if (CollUtil.isNotEmpty(customerFriendList)) {
                if (customerFriendList.size() != 1) {
                    throw new ServiceException("实时绑定表业务处理异常:客户ID[" + customerId + "]与多条好友处于在绑中");
                }

                // 客户有绑定好友就解绑
                CmfUsers cmfUsers = remoteCmfUserService.getCustomerById(convertToLong(customerId), SecurityConstants.INNER).getData();
                CrmCustomerFriend customerFriend = customerFriendList.get(0);
                customerFriend.setEndTime(cmfUsers.getUpdateCreateTime());
                customerFriend.setStatus(1);
                customerFriendService.updateById(customerFriend);
            }
        }
    }

    protected void handleBusinessLogicUUidDevice(Map<String, SubscribeConvertDbData.Field> fieldMap) {
        // 客户id
        String customerStr = getAfterValueByFieldName("user_id", fieldMap);
        Long customerId = Long.valueOf(customerStr);

        // 现推广归属
        String extendStr = getAfterValueByFieldName("admin_id", fieldMap);
        Long extendId = Long.valueOf(extendStr);

        CrmCustomer crmCustomer = customerService.getById(customerId);

        if (ObjUtil.isNull(crmCustomer)) {
            throw new ServiceException("客户推广绑定表业务处理类异常:找不到此客户ID[" + customerId + "]信息!");
        }

        if (StrUtil.isNotBlank(crmCustomer.getUuidDevice())) {   // 设备编码不为空去判断是否有相同的
            List<CrmCustomer> customerList = customerService.getBaseMapper().selectList(new LambdaQueryWrapper<CrmCustomer>()
                    .eq(CrmCustomer::getUuidDevice, crmCustomer.getUuidDevice()));

            if (CollUtil.isEmpty(customerList)) {
                throw new ServiceException("客户信息业务处理类异常:根据设备码去匹配无数据!");
            }
            if (customerList.size() == 1) {
                return;                 // 没有相同的设备码客户
            }

            List<Long> customerIds = customerList.stream().map(CrmCustomer::getCustomerId).distinct().toList();

            // 在绑的客户集
            List<CrmCustomerFriend> customerFriendList = customerFriendService.getBaseMapper().selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .in(CrmCustomerFriend::getCustomerId, customerIds)
                    .eq(CrmCustomerFriend::getPyExtendId, extendId)
                    .eq(CrmCustomerFriend::getStatus, 0));

            if (CollUtil.isNotEmpty(customerFriendList)) {
                List<Long> friendIds = customerFriendList.stream().map(CrmCustomerFriend::getFriendId).distinct().toList();
                if (friendIds.size() > 1) {
                    throw new ServiceException("客户信息业务处理类异常:此客户的设备编码[" + crmCustomer.getUuidDevice() + "]有多个客户相同,这些客户绑定了多个好友!");
                }

                CrmCustomerFriend customerFriend = new CrmCustomerFriend();
                customerFriend.setFriendId(friendIds.get(0));
                customerFriend.setCustomerId(customerId);
                customerFriend.setPyExtendId(extendId);
                customerFriend.setStatus(0);
                customerFriend.setCreateBy(customerFriendList.get(0).getCreateBy());
                customerFriend.setBeginTime(crmCustomer.getUpdateTime());

                customerFriendService.save(customerFriend);
            }
        }
    }
}
