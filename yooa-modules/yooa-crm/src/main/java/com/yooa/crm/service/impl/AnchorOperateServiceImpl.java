package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmAnchorOperate;
import com.yooa.crm.mapper.CrmAnchorOperateMapper;
import com.yooa.crm.service.AnchorOperateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 主播运营关联 - 服务实现层
 */
@Service
@RequiredArgsConstructor
public class AnchorOperateServiceImpl extends ServiceImpl<CrmAnchorOperateMapper, CrmAnchorOperate> implements AnchorOperateService {


    /**
     * 根据主播id获取最新一条记录
     * @param anchorId 主播id
     * @return
     */
    @Override
    public CrmAnchorOperate getAnchorOperateByAnchorIdLast(String anchorId) {
        return getOne(new LambdaQueryWrapper<CrmAnchorOperate>()
                .eq(CrmAnchorOperate::getAnchorId,anchorId)
                .orderByDesc(CrmAnchorOperate::getId)
                .last("limit 1"));
    }

    @Override
    public Long getPyOperateIdByAnchorId(Long anchorId) {
        return   Optional.ofNullable(getBaseMapper().selectOne(new LambdaQueryWrapper<CrmAnchorOperate>()
                .eq(CrmAnchorOperate::getAnchorId, anchorId)
                .isNull(CrmAnchorOperate::getLoseTime)
                .last("limit 1")))
                .map(CrmAnchorOperate::getOperateId)
                .orElse(null);
    }

}
