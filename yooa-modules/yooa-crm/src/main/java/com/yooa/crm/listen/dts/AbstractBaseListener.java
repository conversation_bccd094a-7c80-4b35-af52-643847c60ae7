package com.yooa.crm.listen.dts;

import com.yooa.common.core.constant.KafkaTopicConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.retry.annotation.Backoff;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public abstract class AbstractBaseListener extends BaseKafkaHandler {


    @Override
    @KafkaListener(topics = "_cdc_" + "#{__listener.getTopic()}", groupId = KafkaTopicConstants.GROUP_ID)
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 2000L, multiplier = 2),
            dltTopicSuffix = "-dlt",
            exclude = DataIntegrityViolationException.class
    )
    public void commonListener(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        super.commonListener(consumerRecord, acknowledgment);
    }

    /**
     * 死信队列处理异常消息
     */
    @KafkaListener(topics = "_cdc_" + "#{__listener.getTopic()}" + "-dlt", groupId = "dlt-group")
    public void saveErrLog(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment,
            @Header(KafkaHeaders.EXCEPTION_MESSAGE) String exceptionMessage,
            @Header(KafkaHeaders.EXCEPTION_STACKTRACE) String exceptionStackTrace,
            @Header(KafkaHeaders.ORIGINAL_TOPIC) String originalTopic) {
        log.warn("死信队列收到消息 topic:{}", originalTopic);
        // 获取自定义异常信息
        String errMsg = extractBaseExceptionMessage(exceptionStackTrace, exceptionMessage);

        // 保存异常日志
        saveErrorAsync(parseRecord(consumerRecord), errMsg, originalTopic);
        // 处理其他业务
        dltBusiness(consumerRecord, acknowledgment);
    }

    // 判断是否为自定义异常
    private boolean isBaseException(String stackTrace) {
        return stackTrace != null &&
                stackTrace.contains("com.yooa.common.core.exception.ServiceException");
    }

    // 获取自定义异常信息
    private String extractBaseExceptionMessage(String stackTrace, String exceptionMessage) {
        if (stackTrace == null) {
            return "";
        }
        Pattern pattern = Pattern.compile("ServiceException:\\s*(.*?)(?=\\s*at\\b|$)");
        Matcher matcher = pattern.matcher(stackTrace);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        return exceptionMessage;
    }


    public abstract void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment);


}
