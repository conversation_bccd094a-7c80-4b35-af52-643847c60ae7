package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerFollowRecord;
import com.yooa.crm.api.domain.query.CustomerFollowRecordQuery;
import com.yooa.crm.api.domain.vo.CustomerFollowRecordVo;

import java.util.List;

/**
 * 客户跟踪记录 - 服务层
 */
public interface CustomerFollowRecordService extends IService<CrmCustomerFollowRecord> {

    List<CustomerFollowRecordVo> list(CustomerFollowRecordQuery query);
}
