package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.crm.api.domain.CrmCustomerFollowComment;
import com.yooa.crm.api.domain.vo.CustomerFollowCommentVo;

import java.util.List;

/**
 * 客户跟踪评论 - 数据层
 */
public interface CrmCustomerFollowCommentMapper extends BaseMapper<CrmCustomerFollowComment> {

    /**
     * 查询跟踪记录的评论列表
     */
    List<CustomerFollowCommentVo> selectList(Long followId);

}




