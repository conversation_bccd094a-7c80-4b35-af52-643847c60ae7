package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmFriendConfirm;
import com.yooa.crm.api.domain.query.FriendConfirmQuery;
import com.yooa.crm.api.domain.vo.FriendConfirmVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 好友确认 - 数据层
 */
public interface CrmFriendConfirmMapper extends BaseMapper<CrmFriendConfirm> {

    List<FriendConfirmVo> selectListByExtend(Page<FriendConfirmVo> page, @Param("query") FriendConfirmQuery query);

    List<FriendConfirmVo> selectListByPitcher(Page<FriendConfirmVo> page, @Param("query") FriendConfirmQuery query);

    FriendConfirmVo selectByConfirmId(@Param("confirmId") Long confirmId);
}




