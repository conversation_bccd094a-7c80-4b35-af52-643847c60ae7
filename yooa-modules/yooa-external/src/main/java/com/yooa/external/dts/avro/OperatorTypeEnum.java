package com.yooa.external.dts.avro;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.lang.Integer;

@AllArgsConstructor
@NoArgsConstructor
public enum OperatorTypeEnum {
    INSERT(1, "新增"),
    UPDATE(2, "修改"),
    DELETE(3, "删除");

    private Integer code;
    private String msg;

    public static Integer match(Operation operation) {
        OperatorTypeEnum[] values = OperatorTypeEnum.values();
        for (OperatorTypeEnum value : values) {
            if (StrUtil.equals(operation.name(), value.name())) {
                return value.code;
            }
        }
        return null;
    }
}
