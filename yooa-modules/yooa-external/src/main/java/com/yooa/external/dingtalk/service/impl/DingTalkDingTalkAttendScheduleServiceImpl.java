package com.yooa.external.dingtalk.service.impl;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAttendanceScheduleListbyusersRequest;
import com.dingtalk.api.response.OapiAttendanceScheduleListbyusersResponse;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.external.api.request.DingTalkAttendScheduleRequest;
import com.yooa.external.api.response.DingTalkAttendScheduleRes;
import com.yooa.external.dingtalk.service.DingTalkAccessTokenService;
import com.yooa.external.dingtalk.service.DingTalkAttendScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class DingTalkDingTalkAttendScheduleServiceImpl implements DingTalkAttendScheduleService {

    @Autowired
    private DingTalkAccessTokenService dingTalkAccessTokenService;


    @Override
    public List<DingTalkAttendScheduleRes> syncAttendSchedule(DingTalkAttendScheduleRequest dingTalkAttendScheduleRequest) {

        List<DingTalkAttendScheduleRes> list = new ArrayList<>();
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/attendance/schedule/listbyusers");
            OapiAttendanceScheduleListbyusersRequest req = new OapiAttendanceScheduleListbyusersRequest();
            req.setOpUserId("17423475692064659");
            req.setUserids(dingTalkAttendScheduleRequest.getUserIds());
            req.setFromDateTime(dingTalkAttendScheduleRequest.getStartTime());
            req.setToDateTime(dingTalkAttendScheduleRequest.getEndTime());
            OapiAttendanceScheduleListbyusersResponse rsp = client.execute(req, dingTalkAccessTokenService.getAccessToken());
            if (rsp.getErrcode() != 0) {
                throw new ServiceException("获取用户排班数据失败:" + rsp.getErrmsg());
            }
            List<OapiAttendanceScheduleListbyusersResponse.TopScheduleVo> topScheduleVoList = rsp.getResult();
            detailData(topScheduleVoList, list);
        }
        catch (Exception e) {
            throw new ServiceException("获取用户排班数据失败:" + e.getMessage());
        }
        return list;
    }


    private List<DingTalkAttendScheduleRes> detailData(List<OapiAttendanceScheduleListbyusersResponse.TopScheduleVo> topScheduleVoList, List<DingTalkAttendScheduleRes> list) {
        if (topScheduleVoList == null || topScheduleVoList.isEmpty()) {
            return list;
        }
        for (OapiAttendanceScheduleListbyusersResponse.TopScheduleVo topScheduleVo : topScheduleVoList) {
            DingTalkAttendScheduleRes dingTalkAttendScheduleRes = new DingTalkAttendScheduleRes();
            dingTalkAttendScheduleRes.setClassId(topScheduleVo.getShiftId());
            dingTalkAttendScheduleRes.setGroupId(topScheduleVo.getGroupId());
            dingTalkAttendScheduleRes.setUserId(topScheduleVo.getUserid());
            dingTalkAttendScheduleRes.setPlanDate(topScheduleVo.getWorkDate());
            dingTalkAttendScheduleRes.setPlanCheckTime(topScheduleVo.getPlanCheckTime());
            dingTalkAttendScheduleRes.setCheckType(topScheduleVo.getCheckType());
            list.add(dingTalkAttendScheduleRes);
        }
        return list;
    }
}
