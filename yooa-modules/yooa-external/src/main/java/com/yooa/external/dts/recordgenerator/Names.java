package com.yooa.external.dts.recordgenerator;

public class Names {
    // detail control
    public static final String TRY_TIME = "stream.tryTime";
    public static final String TRY_BACK_TIME_MS = "stream.tryBackTimeMS";
    public static final String RETRY_TIME_OUT = "stream.errorRetryTimeOut";
    public static final String POLL_TIME_OUT = "stream.pool.timeout";
    // general friendName
    public static final String KAFKA_TOPIC = "kafkaTopic";
    public static final String KAFKA_BROKER_URL_NAME = "broker";
    public static final String GROUP_NAME = "group";

    public static final String USE_CONFIG_CHECKPOINT_NAME = "useConfigCheckpoint";
    public static final String SUBSCRIBE_MODE_NAME = "subscribeMode";

    public static final String INITIAL_CHECKPOINT_NAME = "checkpoint";
    public static final String USER_NAME = "user";
    public static final String PASSWORD_NAME = "password";
    public static final String SID_NAME = "sid";
    public static final long MAX_TIMESTAMP_SECOND = 99999999999L;
}
