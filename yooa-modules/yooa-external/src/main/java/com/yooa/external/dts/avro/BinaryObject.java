/**
 * Autogenerated by Avro
 * <p>
 * DO NOT EDIT DIRECTLY
 */
package com.yooa.external.dts.avro;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class BinaryObject extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"BinaryObject\",\"namespace\":\"com.alibaba.dts.formats.avro\",\"fields\":[{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":\"bytes\"}]}");
    private static final long serialVersionUID = 9094139956249411247L;
    private static SpecificData MODEL$ = new SpecificData();
    private static final BinaryMessageEncoder<BinaryObject> ENCODER =
            new BinaryMessageEncoder<BinaryObject>(MODEL$, SCHEMA$);
    private static final BinaryMessageDecoder<BinaryObject> DECODER =
            new BinaryMessageDecoder<BinaryObject>(MODEL$, SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumWriter<BinaryObject>
            WRITER$ = (org.apache.avro.io.DatumWriter<BinaryObject>) MODEL$.createDatumWriter(SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumReader<BinaryObject>
            READER$ = (org.apache.avro.io.DatumReader<BinaryObject>) MODEL$.createDatumReader(SCHEMA$);
    @Deprecated
    public String type;
    @Deprecated
    public java.nio.ByteBuffer value;

    /**
     * Default constructor.  Note that this does not initialize fields
     * to their default values from the schema.  If that is desired then
     * one should use <code>newBuilder()</code>.
     */
    public BinaryObject() {
    }

    /**
     * All-args constructor.
     *
     * @param type  The new value for type
     * @param value The new value for value
     */
    public BinaryObject(String type, java.nio.ByteBuffer value) {
        this.type = type;
        this.value = value;
    }

    public static org.apache.avro.Schema getClassSchema() {
        return SCHEMA$;
    }

    /**
     * Return the BinaryMessageDecoder instance used by this class.
     */
    public static BinaryMessageDecoder<BinaryObject> getDecoder() {
        return DECODER;
    }

    /**
     * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
     *
     * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
     */
    public static BinaryMessageDecoder<BinaryObject> createDecoder(SchemaStore resolver) {
        return new BinaryMessageDecoder<BinaryObject>(MODEL$, SCHEMA$, resolver);
    }

    /**
     * Deserializes a BinaryObject from a ByteBuffer.
     */
    public static BinaryObject fromByteBuffer(
            java.nio.ByteBuffer b) throws java.io.IOException {
        return DECODER.decode(b);
    }

    /**
     * Creates a new BinaryObject RecordBuilder.
     *
     * @return A new BinaryObject RecordBuilder
     */
    public static BinaryObject.Builder newBuilder() {
        return new BinaryObject.Builder();
    }

    /**
     * Creates a new BinaryObject RecordBuilder by copying an existing Builder.
     *
     * @param other The existing builder to copy.
     * @return A new BinaryObject RecordBuilder
     */
    public static BinaryObject.Builder newBuilder(BinaryObject.Builder other) {
        return new BinaryObject.Builder(other);
    }

    /**
     * Creates a new BinaryObject RecordBuilder by copying an existing BinaryObject instance.
     *
     * @param other The existing instance to copy.
     * @return A new BinaryObject RecordBuilder
     */
    public static BinaryObject.Builder newBuilder(BinaryObject other) {
        return new BinaryObject.Builder(other);
    }

    /**
     * Serializes this BinaryObject to a ByteBuffer.
     */
    public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
        return ENCODER.encode(this);
    }

    public org.apache.avro.Schema getSchema() {
        return SCHEMA$;
    }

    // Used by DatumWriter.  Applications should not call.
    public Object get(int field$) {
        switch (field$) {
            case 0:
                return type;
            case 1:
                return value;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    // Used by DatumReader.  Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int field$, Object value$) {
        switch (field$) {
            case 0:
                type = (String) value$;
                break;
            case 1:
                value = (java.nio.ByteBuffer) value$;
                break;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    /**
     * Gets the value of the 'type' field.
     *
     * @return The value of the 'type' field.
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the 'type' field.
     *
     * @param value the value to set.
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the 'value' field.
     *
     * @return The value of the 'value' field.
     */
    public java.nio.ByteBuffer getValue() {
        return value;
    }

    /**
     * Sets the value of the 'value' field.
     *
     * @param value the value to set.
     */
    public void setValue(java.nio.ByteBuffer value) {
        this.value = value;
    }

    @Override
    public void writeExternal(java.io.ObjectOutput out)
            throws java.io.IOException {
        WRITER$.write(this, SpecificData.getEncoder(out));
    }

    @Override
    public void readExternal(java.io.ObjectInput in)
            throws java.io.IOException {
        READER$.read(this, SpecificData.getDecoder(in));
    }

    /**
     * RecordBuilder for BinaryObject instances.
     */
    public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<BinaryObject>
            implements org.apache.avro.data.RecordBuilder<BinaryObject> {

        private String type;
        private java.nio.ByteBuffer value;

        /**
         * Creates a new Builder
         */
        private Builder() {
            super(SCHEMA$);
        }

        /**
         * Creates a Builder by copying an existing Builder.
         *
         * @param other The existing Builder to copy.
         */
        private Builder(BinaryObject.Builder other) {
            super(other);
            if (isValidValue(fields()[0], other.type)) {
                this.type = data().deepCopy(fields()[0].schema(), other.type);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.value)) {
                this.value = data().deepCopy(fields()[1].schema(), other.value);
                fieldSetFlags()[1] = true;
            }
        }

        /**
         * Creates a Builder by copying an existing BinaryObject instance
         *
         * @param other The existing instance to copy.
         */
        private Builder(BinaryObject other) {
            super(SCHEMA$);
            if (isValidValue(fields()[0], other.type)) {
                this.type = data().deepCopy(fields()[0].schema(), other.type);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.value)) {
                this.value = data().deepCopy(fields()[1].schema(), other.value);
                fieldSetFlags()[1] = true;
            }
        }

        /**
         * Gets the value of the 'type' field.
         *
         * @return The value.
         */
        public String getType() {
            return type;
        }

        /**
         * Sets the value of the 'type' field.
         *
         * @param value The value of 'type'.
         * @return This builder.
         */
        public BinaryObject.Builder setType(String value) {
            validate(fields()[0], value);
            this.type = value;
            fieldSetFlags()[0] = true;
            return this;
        }

        /**
         * Checks whether the 'type' field has been set.
         *
         * @return True if the 'type' field has been set, false otherwise.
         */
        public boolean hasType() {
            return fieldSetFlags()[0];
        }


        /**
         * Clears the value of the 'type' field.
         *
         * @return This builder.
         */
        public BinaryObject.Builder clearType() {
            type = null;
            fieldSetFlags()[0] = false;
            return this;
        }

        /**
         * Gets the value of the 'value' field.
         *
         * @return The value.
         */
        public java.nio.ByteBuffer getValue() {
            return value;
        }

        /**
         * Sets the value of the 'value' field.
         *
         * @param value The value of 'value'.
         * @return This builder.
         */
        public BinaryObject.Builder setValue(java.nio.ByteBuffer value) {
            validate(fields()[1], value);
            this.value = value;
            fieldSetFlags()[1] = true;
            return this;
        }

        /**
         * Checks whether the 'value' field has been set.
         *
         * @return True if the 'value' field has been set, false otherwise.
         */
        public boolean hasValue() {
            return fieldSetFlags()[1];
        }


        /**
         * Clears the value of the 'value' field.
         *
         * @return This builder.
         */
        public BinaryObject.Builder clearValue() {
            value = null;
            fieldSetFlags()[1] = false;
            return this;
        }

        @Override
        @SuppressWarnings("unchecked")
        public BinaryObject build() {
            try {
                BinaryObject record = new BinaryObject();
                record.type = fieldSetFlags()[0] ? this.type : (String) defaultValue(fields()[0]);
                record.value = fieldSetFlags()[1] ? this.value : (java.nio.ByteBuffer) defaultValue(fields()[1]);
                return record;
            }
            catch (Exception e) {
                throw new org.apache.avro.AvroRuntimeException(e);
            }
        }
    }

}
