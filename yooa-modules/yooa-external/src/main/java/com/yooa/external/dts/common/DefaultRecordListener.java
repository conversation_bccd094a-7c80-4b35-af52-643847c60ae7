package com.yooa.external.dts.common;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.utils.SpringUtils;
import com.yooa.external.dts.avro.Operation;
import com.yooa.external.dts.boot.RecordPrinter;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class DefaultRecordListener implements RecordListener {

    private static final Logger log = LoggerFactory.getLogger(DefaultRecordListener.class);
    /**
     * 过滤需要发送到我们自己kafka的表名
     * 客户表 cmf_users
     * 充值表 cmf_users_charge
     * 打赏表 cmf_users_coinrecord
     * 客户推广绑定表 cmf_agent_admin_and_user
     * 客户推广绑定记录表 cmf_agent_admin_and_user_log
     * 交接客服表 cmf_agent_admin_join_serve
     * 交接主播表 cmf_users_join_live
     * TODO cmf_users_variation_log
     * cmf_record_back 退款表
     * cmf_users_liverecord 直播记录表
     * cmf_three_charge_back 充值退款表
     */
//    private List<String> filterDbTableNameList = ImmutableList.of("cmf_users","cmf_users_charge","cmf_users_coinrecord",
//            "cmf_agent_admin_and_user","cmf_agent_admin_and_user_log","cmf_agent_admin_join_serve","cmf_users_join_live","cmf_users_charge");

//    private final List<String> filterDbTableNameList = ImmutableList.of(KafkaTopicConstants.CMF_USERS,KafkaTopicConstants.CMF_USERS_CHARGE,
//            KafkaTopicConstants.CMF_USERS_JOIN_LIVE, KafkaTopicConstants.CMF_AGENT_ADMIN_AND_USER,
//            KafkaTopicConstants.CMF_AGENT_ADMIN_AND_USER_LOG, KafkaTopicConstants.CMF_AGENT_ADMIN_JOIN_SERVE,KafkaTopicConstants.CMF_USERS_VARIATION_LOG,
//            KafkaTopicConstants.CMF_THREE_CHARGE_BACK,
//            KafkaTopicConstants.CMF_USERS_VARIATION_LOG);

    // 越写越多  干脆写个方法
    private final List<String> filterDbTableNameList = KafkaTopicConstants.TOPIC_LIST;
    private String dbType;
    private RecordPrinter recordPrinter;


    public DefaultRecordListener(String dbType) {
        this.dbType = dbType;

        recordPrinter = new RecordPrinter(dbType);
    }


    @Override
    public void consume(UserRecord record) {

        Operation operation = record.getRecord().getOperation();
        if (operation.equals(Operation.INSERT)
                || operation.equals(Operation.UPDATE)
                || operation.equals(Operation.DELETE)
                || operation.equals(Operation.DDL)
                || operation.equals(Operation.HEARTBEAT)) {
            // consume record
            SubscribeConvertDbData ret = recordPrinter.recordToListenConvert(record.getRecord());

            // 根据表名发送不同topic
            if (filterDbTableNameList.contains(ret.getDbTableName())) {
                try {
                    String dbTableName = ret.getDbTableName();
                    String topic = StrUtil.format("_cdc_{}", dbTableName);
                    KafkaProducer kafkaProducer = SpringUtils.getBean(KafkaProducer.class);
                    ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, JSONObject.toJSONString(ret));
                    // 发送到自己kafka进行消费
                    kafkaProducer.send(producerRecord);
                    //  log.info(ret);
                    log.info("listener message: dbName:[{}] dbTableName:[{}] id:[{}] operatorType:[{}] \r\n", ret.getDbTableName(), ret.getDbTableName(), ret.getFieldList().get(0).getAfterValue(), ret.getOperatorType());
                }
                catch (Exception e) {
                    log.error("kafka producer send error", e);
                }
            }
        }
        // commit
        //    log.info("operation : " + operation + ", timestamp: " + record.getRecord().getSourceTimestamp());
        record.commit(String.valueOf(record.getRecord().getSourceTimestamp()));
    }
}
