package org.openoa.base.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.openoa.base.entity.Role;
import org.openoa.base.entity.User;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/17 22:34
 * @Version 1.0
 */
@Mapper
public interface RoleMapper {
    List<Role> queryRoleByIds(@Param("roleIds") Collection<String> roleIds);
    List<User> queryUserByRoleIds(@Param("roleIds") Collection<String> roleIds);

    LinkedList<BaseIdTranStruVo> selectAll();
}
