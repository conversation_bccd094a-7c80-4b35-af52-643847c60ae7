package org.openoa.base.util;

import org.apache.commons.lang3.StringUtils;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.base.exception.JiMuBizException;

/**
 *<AUTHOR>
 * @Description //TODO $
 * @Date 2022-05-11 15:14
 * @Param
 * @return
 * @Version 1.0
 */

public class SecurityUtils {
    public static BaseIdTranStruVo getLogInEmpInfo(){
        return (BaseIdTranStruVo) ThreadLocalContainer.get("currentuser");
    }
    public static String getLogInEmpId(){
        String userId =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getUserId());

        if(StringUtils.isBlank(userId)){
            throw new JiMuBizException("当前用户未登陆!");
        }
        return userId;
    }
    public static String getLogInEmpIdStr(){
        String userId =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getUserId());

        if(StringUtils.isBlank(userId)){
            throw new JiMuBizException("当前用户未登陆!!");
        }
        return userId;
    }

    public static String getLogInEmpName(){
        String nickName =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getNickName());

        if(StringUtils.isBlank(nickName)){
            throw new JiMuBizException("当前用户未登陆!!!");
        }
        return nickName;
    }
    public static String getLogInEmpNameSafe(){
        String nickName =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getNickName());

        if(StringUtils.isBlank(nickName)){
            throw new JiMuBizException("当前用户未登陆!!!!");
        }
        return nickName;
    }

    public static String getUserName(){
        String userName =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getUsername());

        if(StringUtils.isBlank(userName)){
            throw new JiMuBizException("当前用户未登陆!!!!");
        }
        return userName;
    }
    public static String getLogInEmpIdSafe(){
        String userId =String.valueOf(com.yooa.common.security.utils.SecurityUtils.getUserId());

        if(StringUtils.isBlank(userId)){
            throw new JiMuBizException("当前用户未登陆!!!!!");
        }
        return userId;
    }
}
