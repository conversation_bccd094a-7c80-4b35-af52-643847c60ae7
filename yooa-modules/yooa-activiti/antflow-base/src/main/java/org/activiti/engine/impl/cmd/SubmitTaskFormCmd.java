/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.activiti.engine.impl.cmd;

import java.util.Map;

import org.activiti.engine.impl.form.TaskFormHandler;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.TaskEntity;


/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class SubmitTaskFormCmd extends NeedsActiveTaskCmd<Object> {

  private static final long serialVersionUID = 1L;
  
  protected String taskId;
  protected Map<String, String> properties;
  protected boolean completeTask;

  public SubmitTaskFormCmd(String taskId, Map<String, String> properties, boolean completeTask) {
    super(taskId);
    this.taskId = taskId;
    this.properties = properties;
    this.completeTask = completeTask;
  }
  
  protected Object execute(CommandContext commandContext, TaskEntity task) {
    commandContext.getHistoryManager()
      .reportFormPropertiesSubmitted(task.getExecution(), properties, taskId);
    
    TaskFormHandler taskFormHandler = task.getTaskDefinition().getTaskFormHandler();
    taskFormHandler.submitFormProperties(properties, task.getExecution());

    if (completeTask) {
      task.complete(properties, false);
    }

    return null;
  }
  
  @Override
  protected String getSuspendedTaskException() {
    return "Cannot submit a form to a suspended task";
  }
  
}
