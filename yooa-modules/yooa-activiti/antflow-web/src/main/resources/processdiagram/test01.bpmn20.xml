<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn"
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
             typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="http://www.zioer.com/reimbursement-9">
    <process id="reimbursement-9" name="费用报销-9" isExecutable="true">
        <startEvent id="startevent1" name="Start" activiti:initiator="startUserId"
                    activiti:formKey="start.form"></startEvent>
        <userTask id="usertask1" name="部门领导审批" activiti:candidateGroups="leadergroup"
                  activiti:formKey="conform1.form"></userTask>
        <userTask id="usertask2" name="财务部门审批" activiti:candidateGroups="feegroup"
                  activiti:formKey="conform2.form"></userTask>
        <userTask id="usertask3" name="申请人确认" activiti:assignee="${startUserId}"
                  activiti:formKey="conform3.form"></userTask>
        <endEvent id="endevent1" name="End"></endEvent>
        <sequenceFlow id="flow7" sourceRef="usertask3" targetRef="endevent1"></sequenceFlow>
        <inclusiveGateway id="inclusivegateway1" name="Inclusive Gateway"></inclusiveGateway>
        <inclusiveGateway id="inclusivegateway2" name="Inclusive Gateway"></inclusiveGateway>
        <sequenceFlow id="flow1" sourceRef="startevent1" targetRef="inclusivegateway1"></sequenceFlow>
        <sequenceFlow id="flow2" sourceRef="inclusivegateway1" targetRef="usertask1">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${fee>=800}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow3" sourceRef="inclusivegateway1" targetRef="usertask2">
            <conditionExpression xsi:type="tFormalExpression"><![CDATA[${fee<1000}]]></conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow4" sourceRef="usertask1" targetRef="inclusivegateway2"></sequenceFlow>
        <sequenceFlow id="flow5" sourceRef="usertask2" targetRef="inclusivegateway2"></sequenceFlow>
        <sequenceFlow id="flow6" sourceRef="inclusivegateway2" targetRef="usertask3"></sequenceFlow>
    </process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_reimbursement-9">
        <bpmndi:BPMNPlane bpmnElement="reimbursement-9" id="BPMNPlane_reimbursement-9">
            <bpmndi:BPMNShape bpmnElement="startevent1" id="BPMNShape_startevent1">
                <omgdc:Bounds height="35.0" width="35.0" x="109.0" y="147.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="usertask1" id="BPMNShape_usertask1">
                <omgdc:Bounds height="55.0" width="105.0" x="280.0" y="90.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="usertask2" id="BPMNShape_usertask2">
                <omgdc:Bounds height="55.0" width="105.0" x="280.0" y="181.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="usertask3" id="BPMNShape_usertask3">
                <omgdc:Bounds height="55.0" width="105.0" x="520.0" y="137.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="endevent1" id="BPMNShape_endevent1">
                <omgdc:Bounds height="35.0" width="35.0" x="680.0" y="147.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="inclusivegateway1" id="BPMNShape_inclusivegateway1">
                <omgdc:Bounds height="40.0" width="40.0" x="190.0" y="144.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape bpmnElement="inclusivegateway2" id="BPMNShape_inclusivegateway2">
                <omgdc:Bounds height="40.0" width="40.0" x="430.0" y="144.0"></omgdc:Bounds>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
                <omgdi:waypoint x="625.0" y="164.0"></omgdi:waypoint>
                <omgdi:waypoint x="680.0" y="164.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
                <omgdi:waypoint x="144.0" y="164.0"></omgdi:waypoint>
                <omgdi:waypoint x="190.0" y="164.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
                <omgdi:waypoint x="210.0" y="144.0"></omgdi:waypoint>
                <omgdi:waypoint x="210.0" y="117.0"></omgdi:waypoint>
                <omgdi:waypoint x="280.0" y="117.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
                <omgdi:waypoint x="210.0" y="184.0"></omgdi:waypoint>
                <omgdi:waypoint x="210.0" y="208.0"></omgdi:waypoint>
                <omgdi:waypoint x="280.0" y="208.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
                <omgdi:waypoint x="385.0" y="117.0"></omgdi:waypoint>
                <omgdi:waypoint x="450.0" y="117.0"></omgdi:waypoint>
                <omgdi:waypoint x="450.0" y="144.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
                <omgdi:waypoint x="385.0" y="208.0"></omgdi:waypoint>
                <omgdi:waypoint x="450.0" y="208.0"></omgdi:waypoint>
                <omgdi:waypoint x="450.0" y="184.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
                <omgdi:waypoint x="470.0" y="164.0"></omgdi:waypoint>
                <omgdi:waypoint x="520.0" y="164.0"></omgdi:waypoint>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>
