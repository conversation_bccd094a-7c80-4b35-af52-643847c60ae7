package org.openoa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;

@Data
@TableName("t_dict_data_depts")
public class DicDateDepts extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;
    @TableField("dic_id")
    private  Long DidId;
    @TableField("permission_id")
    private  String permissionId;
    @TableField("permissions_type")
    private  String permissionsType;


}

