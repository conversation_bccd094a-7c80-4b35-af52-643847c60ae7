package org.openoa.engine.bpmnconf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.openoa.engine.bpmnconf.confentity.OutSideBpmnNodeConditionsConf;
import org.openoa.engine.bpmnconf.mapper.OutSideBpmnNodeConditionsConfMapper;
import org.springframework.stereotype.Service;

@Service
public class OutSideBpmnNodeConditionsConfServiceImpl extends ServiceImpl<OutSideBpmnNodeConditionsConfMapper, OutSideBpmnNodeConditionsConf> {


}
