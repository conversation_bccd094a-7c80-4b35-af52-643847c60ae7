<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.openoa.engine.bpmnconf.mapper.BsMapper">

    <sql id="pageFilterSql">
        <if test="pageFilters != null and pageFilters.size > 0">
            <foreach collection="pageFilters" item="pageFilter">
                <if test="pageFilter.filterData != null and pageFilter.filterData.size > 0">
                    AND
                    <foreach collection="pageFilter.filterData" item="item" open="(" separator="OR" close=")">
                        <choose>
                            <when test="item eq null or item eq ''">
                                ${pageFilter.filterName} IS NULL OR ${pageFilter.filterName} = ''
                            </when>
                            <otherwise>
                                ${pageFilter.filterName} = #{item}
                            </otherwise>
                        </choose>
                    </foreach>
                </if>
            </foreach>
        </if>
    </sql>

    <select id="baseIdTran" resultType="org.openoa.base.vo.BaseIdTranStruVo">

        SELECT ${id} AS `id`, ${filName} AS `name`
        FROM ${tableName}
        WHERE is_del = 0
          AND id = #{id}

    </select>

</mapper>