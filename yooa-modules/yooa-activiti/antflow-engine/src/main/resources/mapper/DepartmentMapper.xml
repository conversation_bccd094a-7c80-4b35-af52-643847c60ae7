<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.openoa.engine.bpmnconf.mapper.DepartmentMapper">

    <select id="ListSubDepartmentByEmployeeId" resultType="org.openoa.engine.bpmnconf.confentity.Department">
        select id,name from t_department
        <where>
            <if test="employeeId != null">
                id = #{employeeId}
            </if>
        </where>
    </select>
    <select id="getDepartmentByEmployeeId" resultType="org.openoa.engine.bpmnconf.confentity.Department">
        select id,name from t_department
        <where>
            <if test="employeeId != null">
                id = #{employeeId}
            </if>
        </where>
    </select>
</mapper>