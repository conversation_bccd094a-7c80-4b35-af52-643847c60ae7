<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.openoa.base.mapper.RoleMapper">

    <select id="queryRoleByIds" resultType="org.openoa.base.entity.Role">
        select id,role_name as roleName from t_role
        <where>
            <if test="roleIds != null and roleIds.size() > 0">
                id in
                <foreach collection="roleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryUserByRoleIds" resultType="org.openoa.base.entity.User">
        SELECT
        u.user_id as id,
        u.user_name as userName
        FROM
        sys_user u
        LEFT JOIN sys_user_role s ON u.user_id = s.user_id
        <where>
            <if test="roleIds != null and roleIds.size() > 0">
                s.role_id in
                <foreach collection="roleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAll" resultType="org.openoa.base.vo.BaseIdTranStruVo">
        select id ,role_name as name
         from t_role
    </select>
</mapper>
