package com.yooa.extend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.dto.ExtendVermicelliDto;
import com.yooa.extend.api.domain.vo.VipVermicelliVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【vip_vermicelli(充值达成_粉丝登记列表)】的数据库操作Service
 * @createDate 2025-03-21 16:19:27
 */
public interface VipVermicelliService extends IService<VipVermicelli> {

    // 查询粉丝登记列表(权限(下级))
    List<VipVermicelliVo> selVermicelliListLimits(Page<VipVermicelliVo> page, ExtendVermicelliDto query, boolean bl);

    // 查询粉丝登记列表(权限(自己))
    List<VipVermicelliVo> selVermicelliListOneself(Page<VipVermicelliVo> page, ExtendVermicelliDto query, boolean bl);

    // 查询粉丝登记列表
    List<VipVermicelli> selVermicelliList(List<Long> userIds, Integer fieldType, List<Long> friendIds, String beginTime, String endTime);


}
