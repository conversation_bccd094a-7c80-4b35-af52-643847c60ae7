package com.yooa.extend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.extend.api.domain.TargetPlanChangeRecord;
import com.yooa.extend.api.domain.vo.TargetPlanChangeRecordVo;
import com.yooa.extend.mapper.TargetPlanChangeRecordMapper;
import com.yooa.extend.service.TargetPlanChangeRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 计划目标变动记录 - 业务实现层
 */
@Service
public class TargetPlanChangeRecordServiceImpl extends ServiceImpl<TargetPlanChangeRecordMapper, TargetPlanChangeRecord>
        implements TargetPlanChangeRecordService {

    @Override
    public int save(Long packId, String status, String remark) {
        TargetPlanChangeRecord targetPlanChangeRecord = new TargetPlanChangeRecord();
        targetPlanChangeRecord.setPackId(packId);
        targetPlanChangeRecord.setStatus(status);
        targetPlanChangeRecord.setRemark(remark);
        return baseMapper.insert(targetPlanChangeRecord);
    }

    @Override
    public List<TargetPlanChangeRecordVo> listByPackId(Long packId) {
        return baseMapper.selectListByPackId(packId);
    }
}
