package com.yooa.extend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.extend.api.domain.GoalGroupFields;
import com.yooa.extend.api.domain.vo.GoalGroupFieldsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GoalGroupFieldsMapper extends BaseMapper<GoalGroupFields> {

    // 查询目标字段分组
    List<GoalGroupFieldsVo> selGroupFields(@Param("groupFields") GoalGroupFields groupFields);

}




