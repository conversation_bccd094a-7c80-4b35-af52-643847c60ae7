package com.yooa.extend.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.crm.api.domain.vo.ManufactureUp;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.dto.ExtendVermicelliDto;
import com.yooa.extend.api.domain.dto.OperateVermicelliDto;
import com.yooa.extend.api.domain.vo.ExtendVermicelliVo;
import com.yooa.extend.api.domain.vo.IndexUserGroupAllVo;
import com.yooa.extend.api.domain.vo.OperateVermicelliVo;
import com.yooa.extend.api.domain.vo.VipVermicelliVo;
import com.yooa.extend.service.ExtendVermicelliService;
import com.yooa.extend.service.OperateVermicelliService;
import com.yooa.extend.service.VipVermicelliService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description 粉丝登记列表
 * <AUTHOR>
 * @Date 2024/12/11 上午11:12
 */
@AllArgsConstructor
@Validated
@RestController
@RequestMapping("vermicelli")
public class VermicelliController extends BaseController {

    private final ExtendVermicelliService extendService;
    private final VipVermicelliService vipService;
    private final OperateVermicelliService operateService;

    /********************************************************推广粉丝达成**********************************************************/

    /**
     * 查询粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selExtendVermicelliListLimits")
    public AjaxResult selExtendVermicelliListLimits(Page<ExtendVermicelliVo> page, @Valid @RequestBody ExtendVermicelliDto query) {
        return AjaxResult.success(page.setRecords(extendService.selVermicelliListLimits(page, query, true)));
    }

    /**
     * 导出粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selExtendVermicelliListLimits/export")
    public void exportExtendLimits(HttpServletResponse response, @Valid ExtendVermicelliDto query) {
        List<ExtendVermicelliVo> list = extendService.selVermicelliListLimits(null, query, false);
        ExcelUtil<ExtendVermicelliVo> util = new ExcelUtil<>(ExtendVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 查询粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selExtendVermicelliListOneself")
    public AjaxResult selExtendVermicelliListOneself(Page<ExtendVermicelliVo> page, @Valid @RequestBody ExtendVermicelliDto query) {
        return AjaxResult.success(page.setRecords(extendService.selVermicelliListOneself(page, query, true)));
    }

    /**
     * 导出粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selExtendVermicelliListOneself/export")
    public void exportExtendOneself(HttpServletResponse response, @Valid ExtendVermicelliDto query) {
        List<ExtendVermicelliVo> list = extendService.selVermicelliListOneself(null, query, false);
        ExcelUtil<ExtendVermicelliVo> util = new ExcelUtil<>(ExtendVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 生成粉丝登记列表
     *
     * @param pdExtendIds  PD推广用户id集
     * @param extendId     推广用户id
     * @param extendDeptId 推广用户部门id
     * @param serveId      客服用户id
     * @param serveDeptId  客户用户部门id
     * @param ids          客户ID集
     * @param friendId     好友ID
     */
    @InnerAuth
    @GetMapping("/manufactureVermicelli")
    public R<Integer> manufactureVermicelli(@RequestParam("pdExtendIds") List<Long> pdExtendIds,
                                            @RequestParam("extendId") Long extendId,
                                            @RequestParam("extendDeptId") Long extendDeptId,
                                            @RequestParam(value = "serveId", required = false) Long serveId,
                                            @RequestParam(value = "serveDeptId", required = false) Long serveDeptId,
                                            @RequestParam("ids") List<Long> ids,
                                            @RequestParam("friendId") Long friendId) {
        return R.ok(extendService.manufactureVermicelli(pdExtendIds, extendId, extendDeptId, serveId, serveDeptId, ids, friendId, ManufactureUp.builder().build()));
    }

    /**
     * 生成粉丝登记列表
     *
     * @param pdExtendIds  PD推广用户id集
     * @param extendId     推广用户id
     * @param extendDeptId 推广用户部门id
     * @param serveId      客服用户id
     * @param serveDeptId  客户用户部门id
     * @param ids          客户ID集
     * @param friendId     好友ID
     * @param oneDayUp
     * @param monthUp
     * @return
     */
    @InnerAuth
    @GetMapping("/manufactureVermicelliCopy")
    public R<Integer> manufactureVermicelliCopy(@RequestParam("pdExtendIds") List<Long> pdExtendIds,
                                                @RequestParam("extendId") Long extendId,
                                                @RequestParam("extendDeptId") Long extendDeptId,
                                                @RequestParam(value = "serveId", required = false) Long serveId,
                                                @RequestParam(value = "serveDeptId", required = false) Long serveDeptId,
                                                @RequestParam("ids") List<Long> ids,
                                                @RequestParam("friendId") Long friendId,
                                                @RequestParam(value = "oneDayUp", required = false) BigDecimal oneDayUp,
                                                @RequestParam(value = "monthUp", required = false) BigDecimal monthUp) {
        return R.ok(extendService.manufactureVermicelli(pdExtendIds, extendId, extendDeptId, serveId, serveDeptId, ids, friendId,
                ManufactureUp.builder()
                        .oneDayUp(oneDayUp)
                        .monthUp(monthUp)
                        .build()));
    }

    /**
     * 查询推广粉丝登记汇总数
     *
     * @param userIds OA用户ID集
     */
    @InnerAuth
    @GetMapping("/selExtendVermicelliNumber")
    public R<IndexUserGroupAllVo> selExtendVermicelliNumber(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                            @RequestParam(value = "beginTime", required = false) String beginTime,
                                                            @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(extendService.selVermicelliNumber(userIds, beginTime, endTime));
    }

    /**
     * 查询推广粉丝登记列表(内部)
     *
     * @param userIds OA用户ID集
     */
    @InnerAuth
    @GetMapping("/selExtendVermicelliList")
    public R<List<ExtendVermicelli>> selExtendVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                             @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                             @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                             @RequestParam(value = "beginTime", required = false) String beginTime,
                                                             @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(extendService.selVermicelliList(userIds, fieldType, friendIds, beginTime, endTime));
    }

    /**
     * 查询推广粉丝登记列表
     */
    @InnerAuth
    @GetMapping("/selOrderExtendVermicelliList")
    public R<List<ExtendVermicelli>> selOrderExtendVermicelliList(@RequestParam("userId") Long userId,
                                                                  @RequestParam("customerId") Long customerId,
                                                                  @RequestParam("orderTime") String orderTime) {
        return R.ok(extendService.list(new LambdaQueryWrapper<ExtendVermicelli>()
                .eq(ExtendVermicelli::getExtendId, userId)
                .apply("FIND_IN_SET({0},customer_ids)", customerId)
                .apply("((fans_type IN (0,1,5) AND record_date = {0}) " +
                        "OR (fans_type IN (2,3) AND MONTH(record_date) = MONTH({0})))", orderTime)));
    }

    /**
     * 新增推广粉丝登记
     *
     * @param vermicelli 对象信息
     */
    @InnerAuth
    @PostMapping("/addExtendVermicelli")
    public R<Boolean> addExtendVermicelli(@RequestBody ExtendVermicelli vermicelli) {
        vermicelli.setId(null);
        return R.ok(extendService.save(vermicelli));
    }

    /**
     * 批量修改推广粉丝登记列表
     *
     * @param vermicelliList 对象信息集
     */
    @InnerAuth
    @PostMapping("/updExtendVermicelliList")
    public R<Boolean> updExtendVermicelliList(@RequestBody List<ExtendVermicelli> vermicelliList) {
        return R.ok(extendService.updateBatchById(vermicelliList));
    }

    /**
     * 批量修改推广粉丝登记列表
     *
     * @param ids 对象ID集
     */
    @InnerAuth
    @PostMapping("/removeExtendVermicelliByIds")
    public R<Boolean> removeExtendVermicelliByIds(@RequestBody List<Long> ids) {
        return R.ok(extendService.removeBatchByIds(ids));
    }

    /********************************************************VIP粉丝达成**********************************************************/

    /**
     * 查询粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selVipVermicelliListLimits")
    public AjaxResult selVipVermicelliListLimits(Page<VipVermicelliVo> page, @Valid @RequestBody ExtendVermicelliDto query) {
        return AjaxResult.success(page.setRecords(vipService.selVermicelliListLimits(page, query, true)));
    }

    /**
     * 导出粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selVipVermicelliListLimits/export")
    public void exportVipLimits(HttpServletResponse response, @Valid ExtendVermicelliDto query) {
        List<VipVermicelliVo> list = vipService.selVermicelliListLimits(null, query, false);
        ExcelUtil<VipVermicelliVo> util = new ExcelUtil<>(VipVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 查询粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selVipVermicelliListOneself")
    public AjaxResult selVipVermicelliListOneself(Page<VipVermicelliVo> page, @Valid @RequestBody ExtendVermicelliDto query) {
        return AjaxResult.success(page.setRecords(vipService.selVermicelliListOneself(page, query, true)));
    }

    /**
     * 导出粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selVipVermicelliListOneself/export")
    public void exportVipOneself(HttpServletResponse response, @Valid ExtendVermicelliDto query) {
        List<VipVermicelliVo> list = vipService.selVermicelliListOneself(null, query, false);
        ExcelUtil<VipVermicelliVo> util = new ExcelUtil<>(VipVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 查询VIP粉丝登记列表(内部)
     *
     * @param userIds OA用户ID集
     */
    @InnerAuth
    @GetMapping("/selVipVermicelliList")
    public R<List<VipVermicelli>> selVipVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                       @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                       @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                       @RequestParam(value = "beginTime", required = false) String beginTime,
                                                       @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(vipService.selVermicelliList(userIds, fieldType, friendIds, beginTime, endTime));
    }

    /**
     * 查询VIP粉丝登记列表
     */
    @InnerAuth
    @GetMapping("/selOrderVipVermicelliList")
    public R<List<VipVermicelli>> selOrderVipVermicelliList(@RequestParam("userId") Long userId,
                                                            @RequestParam("customerId") Long customerId,
                                                            @RequestParam("orderTime") String orderTime) {
        return R.ok(vipService.list(new LambdaQueryWrapper<VipVermicelli>()
                .eq(VipVermicelli::getServeId, userId)
                .apply("FIND_IN_SET({0},customer_ids)", customerId)
                .apply("((fans_type IN (0,1,5) AND record_date = {0}) " +
                        "OR (fans_type IN (2,3) AND MONTH(record_date) = MONTH({0})))", orderTime)));
    }

    /**
     * 新增VIP粉丝登记
     *
     * @param vermicelli 对象信息
     */
    @InnerAuth
    @PostMapping("/addVipVermicelli")
    public R<Boolean> addVipVermicelli(@RequestBody VipVermicelli vermicelli) {
        vermicelli.setId(null);
        return R.ok(vipService.save(vermicelli));
    }

    /**
     * 批量修改VIP粉丝登记列表
     *
     * @param vermicelliList 对象信息集
     */
    @InnerAuth
    @PostMapping("/updVipVermicelliList")
    public R<Boolean> updVipVermicelliList(@RequestBody List<VipVermicelli> vermicelliList) {
        return R.ok(vipService.updateBatchById(vermicelliList));
    }

    /**
     * 批量修改VIP粉丝登记列表
     *
     * @param ids 对象ID集
     */
    @InnerAuth
    @PostMapping("/removeVipVermicelliByIds")
    public R<Boolean> removeVipVermicelliByIds(@RequestBody List<Long> ids) {
        return R.ok(vipService.removeBatchByIds(ids));
    }

    /********************************************************运营粉丝达成**********************************************************/

    /**
     * 查询运营粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selOperateVermicelliListLimits")
    public AjaxResult selOperateVermicelliListLimits(Page<OperateVermicelliVo> page, @Valid @RequestBody OperateVermicelliDto query) {
        return AjaxResult.success(page.setRecords(operateService.selOperateVermicelliListLimits(page, query, true)));
    }

    /**
     * 导出运营粉丝登记列表(权限(下级))
     *
     * @return
     */
    @PostMapping("/selOperateVermicelliListLimits/export")
    public void exportOperateLimits(HttpServletResponse response, @Valid OperateVermicelliDto query) {
        List<OperateVermicelliVo> list = operateService.selOperateVermicelliListLimits(null, query, false);
        ExcelUtil<OperateVermicelliVo> util = new ExcelUtil<>(OperateVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 查询运营粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selOperateVermicelliListOneself")
    public AjaxResult selOperateVermicelliListLimitsOneself(Page<OperateVermicelliVo> page, @Valid @RequestBody OperateVermicelliDto query) {
        return AjaxResult.success(page.setRecords(operateService.selOperateVermicelliListLimitsOneself(page, query, true)));
    }

    /**
     * 导出运营粉丝登记列表(权限(自己))
     *
     * @return
     */
    @PostMapping("/selOperateVermicelliListOneself/export")
    public void exportOperateOneself(HttpServletResponse response, @Valid OperateVermicelliDto query) {
        List<OperateVermicelliVo> list = operateService.selOperateVermicelliListLimits(null, query, false);
        ExcelUtil<OperateVermicelliVo> util = new ExcelUtil<>(OperateVermicelliVo.class);
        util.exportExcel(response, list, "导出粉丝登记列表");
    }

    /**
     * 查询运营粉丝登记列表(内部)
     *
     * @param userIds OA用户ID集
     */
    @InnerAuth
    @GetMapping("/selOperateVermicelliList")
    public R<List<OperateVermicelli>> selOperateVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                               @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                               @RequestParam(value = "customerId", required = false) Long customerId,
                                                               @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                               @RequestParam(value = "beginTime", required = false) String beginTime,
                                                               @RequestParam(value = "endTime", required = false) String endTime) {
        return R.ok(operateService.selOperateVermicelliList(userIds, fieldType, customerId, friendIds, beginTime, endTime));
    }

    /**
     * 新增运营粉丝登记
     *
     * @param vermicelli 对象信息
     */
    @InnerAuth
    @PostMapping("/addOperateVermicelli")
    public R<Boolean> addOperateVermicelli(@RequestBody OperateVermicelli vermicelli) {
        vermicelli.setId(null);
        return R.ok(operateService.save(vermicelli));
    }

    /**
     * 批量修改运营粉丝登记列表
     *
     * @param vermicelliList 对象信息集
     */
    @InnerAuth
    @PostMapping("/updOperateVermicelliList")
    public R<Boolean> updOperateVermicelliList(@RequestBody List<OperateVermicelli> vermicelliList) {
        return R.ok(operateService.updateBatchById(vermicelliList));
    }

}
