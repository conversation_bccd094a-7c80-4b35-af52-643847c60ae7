package com.yooa.extend.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExtendVermicelliDto;
import com.yooa.extend.api.domain.vo.AnchorOperateVo;
import com.yooa.extend.api.domain.vo.ExtendVermicelliVo;
import com.yooa.extend.api.domain.vo.VipVermicelliVo;
import com.yooa.extend.mapper.VipVermicelliMapper;
import com.yooa.extend.service.VipVermicelliService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.query.UserQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【vip_vermicelli(充值达成_粉丝登记列表)】的数据库操作Service实现
 * @createDate 2025-03-21 16:19:27
 */
@AllArgsConstructor
@Service
public class VipVermicelliServiceImpl extends ServiceImpl<VipVermicelliMapper, VipVermicelli>
        implements VipVermicelliService {

    private final RemoteUserService remoteUserService;

    @Override
    public List<VipVermicelliVo> selVermicelliListLimits(Page<VipVermicelliVo> page, ExtendVermicelliDto query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                    .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
        }

        List<VipVermicelliVo> list = new ArrayList<>();

        if (bl) {
            // 查询粉丝登记
            list = baseMapper.selVermicelliListLimits(page, query, userIds, bl);

            // 查询客户名
            list.forEach(e -> {
                if (CollUtil.isNotEmpty(e.getAnchorOperateList()) && ObjUtil.isNull(e.getAnchorOperateList().get(0))) {
                    e.setAnchorOperateList(null);
                }
            });
            if (ObjUtil.isNull(query.getCustomerNickName()) && CollUtil.isNotEmpty(list) && bl) {
                List<Long> ids = list.stream().map(VipVermicelliVo::getId).collect(Collectors.toList());
                List<ExtendVermicelliVo> customerNickNameList = baseMapper.selectVermicelliCustomerNickName(ids);
                if (CollUtil.isNotEmpty(customerNickNameList)) {
                    list.forEach(e -> {
                        ExtendVermicelliVo vo = customerNickNameList.stream().filter(c -> c.getId().equals(e.getId())).findFirst().orElse(null);
                        if (ObjUtil.isNotNull(vo)) {
                            e.setCustomerNickName(vo.getCustomerNickName());
                        }
                    });
                }
            }
        } else {
            // 查询粉丝登记
            list = baseMapper.exportVermicelliListLimits(page, query, userIds, bl);
            // 查询运营信息
            List<AnchorOperateVo> voList = baseMapper.selectAnchorOperateVoList();

            // 提前将voList转换为Map，以customerId和userId为键，减少后续查找的时间复杂度
            Map<String, List<AnchorOperateVo>> voMap = voList.stream()
                    .collect(Collectors.groupingBy(v -> v.getCustomerId().toString() + "-" + v.getUserId()));

            list.forEach(e -> {
                String key = Arrays.stream(e.getCustomerIds().split(","))
                        .map(customerId -> customerId + "-" + e.getExtendId()) // 拼接customerId和extendId
                        .filter(voMap::containsKey) // 检查voMap是否包含该键
                        .findFirst()
                        .orElse(null); // 如果找不到匹配的键，则返回null

                if (key != null) {
                    List<AnchorOperateVo> anchorOperateVoList = voMap.get(key);
                    if (CollUtil.isNotEmpty(anchorOperateVoList)) {
                        e.setAnchorOperateList(anchorOperateVoList);

                        StringBuilder anchorOperateJson = new StringBuilder();
                        for (AnchorOperateVo a : anchorOperateVoList) {
                            String deptName = a.getDeptName();
                            if (StrUtil.isNotBlank(a.getAncestorsNames())) {
                                deptName = a.getAncestorsNames() + "-" + deptName;
                            }

                            if (StrUtil.isNotEmpty(deptName)) {
                                anchorOperateJson.append("运营部门:").append(deptName).append("、");
                            }

                            if (StrUtil.isNotEmpty(a.getNickName())) {
                                anchorOperateJson.append("运营负责人:").append(a.getNickName()).append("、");
                            }

                            if (StrUtil.isNotEmpty(a.getAnchorName())) {
                                anchorOperateJson.append("主播:").append(a.getAnchorName()).append("、");
                            }
                        }
                        if (anchorOperateJson.length() > 0) {
                            anchorOperateJson.setLength(anchorOperateJson.length() - 1); // 去掉最后一个"、"
                        }
                        e.setAnchorOperateJson(anchorOperateJson.toString());
                    }
                }
            });
        }

        return list;
    }

    @Override
    public List<VipVermicelliVo> selVermicelliListOneself(Page<VipVermicelliVo> page, ExtendVermicelliDto query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList(query.getUserId());  // 只查自己的

        List<VipVermicelliVo> list = new ArrayList<>();

        if (bl) {
            // 查询粉丝登记
            list = baseMapper.selVermicelliListLimits(page, query, userIds, bl);

            // 查询客户名
            list.forEach(e -> {
                if (CollUtil.isNotEmpty(e.getAnchorOperateList()) && ObjUtil.isNull(e.getAnchorOperateList().get(0))) {
                    e.setAnchorOperateList(null);
                }
            });
            if (ObjUtil.isNull(query.getCustomerNickName()) && CollUtil.isNotEmpty(list) && bl) {
                List<Long> ids = list.stream().map(VipVermicelliVo::getId).collect(Collectors.toList());
                List<ExtendVermicelliVo> customerNickNameList = baseMapper.selectVermicelliCustomerNickName(ids);
                if (CollUtil.isNotEmpty(customerNickNameList)) {
                    list.forEach(e -> {
                        ExtendVermicelliVo vo = customerNickNameList.stream().filter(c -> c.getId().equals(e.getId())).findFirst().orElse(null);
                        if (ObjUtil.isNotNull(vo)) {
                            e.setCustomerNickName(vo.getCustomerNickName());
                        }
                    });
                }
            }
        } else {
            // 查询粉丝登记
            list = baseMapper.exportVermicelliListLimits(page, query, userIds, bl);
            // 查询运营信息
            List<AnchorOperateVo> voList = baseMapper.selectAnchorOperateVoList();

            // 提前将voList转换为Map，以customerId和userId为键，减少后续查找的时间复杂度
            Map<String, List<AnchorOperateVo>> voMap = voList.stream()
                    .collect(Collectors.groupingBy(v -> v.getCustomerId().toString() + "-" + v.getUserId()));

            list.forEach(e -> {
                String key = Arrays.stream(e.getCustomerIds().split(","))
                        .map(customerId -> customerId + "-" + e.getExtendId()) // 拼接customerId和extendId
                        .filter(voMap::containsKey) // 检查voMap是否包含该键
                        .findFirst()
                        .orElse(null); // 如果找不到匹配的键，则返回null

                if (key != null) {
                    List<AnchorOperateVo> anchorOperateVoList = voMap.get(key);
                    if (CollUtil.isNotEmpty(anchorOperateVoList)) {
                        e.setAnchorOperateList(anchorOperateVoList);

                        StringBuilder anchorOperateJson = new StringBuilder();
                        for (AnchorOperateVo a : anchorOperateVoList) {
                            String deptName = a.getDeptName();
                            if (StrUtil.isNotBlank(a.getAncestorsNames())) {
                                deptName = a.getAncestorsNames() + "-" + deptName;
                            }

                            if (StrUtil.isNotEmpty(deptName)) {
                                anchorOperateJson.append("运营部门:").append(deptName).append("、");
                            }

                            if (StrUtil.isNotEmpty(a.getNickName())) {
                                anchorOperateJson.append("运营负责人:").append(a.getNickName()).append("、");
                            }

                            if (StrUtil.isNotEmpty(a.getAnchorName())) {
                                anchorOperateJson.append("主播:").append(a.getAnchorName()).append("、");
                            }
                        }
                        if (anchorOperateJson.length() > 0) {
                            anchorOperateJson.setLength(anchorOperateJson.length() - 1); // 去掉最后一个"、"
                        }
                        e.setAnchorOperateJson(anchorOperateJson.toString());
                    }
                }
            });
        }

        return list;
    }

    @Override
    public List<VipVermicelli> selVermicelliList(List<Long> userIds, Integer fieldType, List<Long> friendIds, String beginTime, String endTime) {

        /**
         * 创建入参条件
         */
        CommonalityDto dto = new CommonalityDto();

        if (CollUtil.isNotEmpty(friendIds)) {
            dto.setFriendIds(friendIds);
        }
        if (CollUtil.isNotEmpty(userIds)) {
            dto.setUserIds(userIds);
        }
        if (ObjUtil.isNotNull(fieldType)) {
            dto.setFields(fieldType);
        }
        if (StrUtil.isNotEmpty(beginTime)) {
            LocalDateTime begin = LocalDateTime.parse(beginTime);
            dto.setBeginTime(begin);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            LocalDateTime end = LocalDateTime.parse(endTime);
            dto.setEndTime(end);
        }

        return baseMapper.indexUserAllVermicelli(dto);
    }

}




