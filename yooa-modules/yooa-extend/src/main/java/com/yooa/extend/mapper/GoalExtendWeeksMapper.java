package com.yooa.extend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.extend.api.domain.GoalExtendWeeks;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.vo.IndexTimeGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GoalExtendWeeksMapper extends BaseMapper<GoalExtendWeeks> {

    /**
     * 以部门结构查询个人、月计划指定的目标字段的值汇总根据时间(月、但默认为月初)分组
     *
     * @param dto (参数:fieldsGoalId、deptId、beginTime、endTime)
     */
    List<IndexTimeGroupVo> spliceGoalData(@Param("dto") CommonalityDto dto);

    // 批量新增
    int insertAll(@Param("list") List<GoalExtendWeeks> extendWeeksList);

}




