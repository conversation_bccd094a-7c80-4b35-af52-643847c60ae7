package com.yooa.extend.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.extend.api.domain.GoalExtendPack;
import com.yooa.extend.api.domain.dto.GoalExtendPackQueryDto;
import com.yooa.extend.mapper.GoalExtendPackMapper;
import com.yooa.extend.service.GoalExtendPackService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class GoalExtendPackServiceImpl extends ServiceImpl<GoalExtendPackMapper, GoalExtendPack> implements GoalExtendPackService {

    @Override
    public List<GoalExtendPack> getGoalExtendPackList(Page<GoalExtendPack> page, GoalExtendPackQueryDto query) {
        return baseMapper.selectGoalExtendPackList(page, query);
    }
}




