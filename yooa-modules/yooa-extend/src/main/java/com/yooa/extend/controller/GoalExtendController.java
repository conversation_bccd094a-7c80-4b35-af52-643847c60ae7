package com.yooa.extend.controller;

import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.domain.GoalFields;
import com.yooa.extend.api.domain.GoalGroupFields;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExamineGoalDto;
import com.yooa.extend.api.domain.dto.UpdGoalExtendMonthDto;
import com.yooa.extend.api.domain.dto.UpdGoalExtendWeeksDto;
import com.yooa.extend.service.GoalExtendService;
import com.yooa.extend.service.TargetPlanChangeRecordService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 *
 */
@AllArgsConstructor
@Validated
@RestController
@RequestMapping("extendGoal")
public class GoalExtendController {

    private GoalExtendService goalExtendService;
    private TargetPlanChangeRecordService targetPlanChangeRecordService;

    /**
     * 制定目标字段
     */
    @PostMapping("/setTargetFields")
    public AjaxResult setTargetFields(@RequestBody GoalFields goalFields) {
        return AjaxResult.success(goalExtendService.setTargetFields(goalFields));
    }

    /**
     * 删除已制定目标字段(目标字段已被使用不能删除!)
     */
    @PostMapping("/delTargetFields")
    public AjaxResult delTargetFields(@RequestBody GoalFields goalFields) {
        return AjaxResult.success(goalExtendService.delTargetFields(goalFields));
    }

    /**
     * 查询制定目标字段
     */
    @PostMapping("/inquireTargetFields")
    public AjaxResult inquireTargetFields(@RequestBody GoalFields goalFields) {
        return AjaxResult.success(goalExtendService.inquireTargetFields(goalFields));
    }

    /**
     * 新增目标字段分组
     */
    @PostMapping("/addGroupFields")
    public AjaxResult addGroupFields(@RequestBody GoalGroupFields groupFields) {
        return AjaxResult.success(goalExtendService.addGroupFields(groupFields));
    }

    /**
     * 查询目标字段分组
     */
    @PostMapping("/selGroupFields")
    public AjaxResult selGroupFields(@RequestBody GoalGroupFields groupFields) {
        return AjaxResult.success(goalExtendService.selGroupFields(groupFields));
    }

    /**
     * 新增计划-月计划-填写周计划目标数值
     */
    @PostMapping("/updWeeksGoal")
    public AjaxResult updWeeksGoal(@Valid @RequestBody UpdGoalExtendWeeksDto updGoalExtendWeeksDto) {
        if (goalExtendService.updWeeksGoal(SecurityUtils.getLoginUser().getSysUser(), updGoalExtendWeeksDto) >= 1) {
            targetPlanChangeRecordService.save(updGoalExtendWeeksDto.getGoalId().longValue(), String.valueOf(updGoalExtendWeeksDto.getAuditStatus()), null);
        }
        return AjaxResult.success();
    }

    /**
     * 新增计划-年计划-填写月计划目标数值
     */
    @PostMapping("/updMonthGoal")
    public AjaxResult updMonthGoal(@Valid @RequestBody UpdGoalExtendMonthDto updGoalExtendMonthDto) {
        if (goalExtendService.updMonthGoal(SecurityUtils.getLoginUser().getSysUser(), updGoalExtendMonthDto) >= 1) {
            targetPlanChangeRecordService.save(updGoalExtendMonthDto.getGoalId().longValue(), String.valueOf(updGoalExtendMonthDto.getAuditStatus()), null);
        }
        return AjaxResult.success();
    }

    /**
     * 新增计划-月计划-查询自己的周计划列表
     */
    @GetMapping("/selWeeksGoal")
    public AjaxResult selWeeksGoal(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selWeeksGoal(commonalityDto));
    }

    /**
     * 新增计划-年计划-查询自己的月计划列表
     */
    @GetMapping("/selMonthGoal")
    public AjaxResult selMonthGoal(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selMonthGoal(commonalityDto));
    }

    /**
     * 我的代办-查看计划详情
     */
    @GetMapping("/selGoalDetails")
    public AjaxResult selGoalDetails(Integer goalId) {
        return AjaxResult.success(goalExtendService.selGoalDetails(goalId));
    }

    /**
     * 我的工作台-查询年计划进度
     */
    @GetMapping("/selYearsSchedule")
    public AjaxResult selYearsSchedule(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selYearsSchedule(commonalityDto));
    }

    /**
     * 我的工作台-查询年计划进度(多条)
     */
    @GetMapping("/selYearsScheduleList")
    public AjaxResult selYearsScheduleList(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selYearsScheduleList(commonalityDto));
    }

    /**
     * 我的工作台-查询计划审核状态
     *
     * @param tableType 0:年计划、1:月计划
     * @return map:k:0/管理员、1/待审核、2/审核通过、3/审核未通过
     */
    @GetMapping("/selPackAuditStatus/{tableType}")
    public AjaxResult selPackAuditStatus(@PathVariable Integer tableType, CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selPackAuditStatus(tableType, commonalityDto));
    }

    /**
     * 我的工作台-本月计划进度-查询月计划进度
     */
    @GetMapping("/selMonthScheduleList")
    public AjaxResult selMonthScheduleList(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selMonthScheduleList(commonalityDto));
    }

    /**
     * 新增计划-月计划-查询月计划计划进度
     */
    @GetMapping("/selMonthSchedule")
    public AjaxResult selMonthSchedule(CommonalityDto commonalityDto) {
        return AjaxResult.success(goalExtendService.selMonthSchedule(commonalityDto));
    }

    /**
     * 查询自己的审批计划列表
     */
    @PostMapping("/examineGoalList")
    public AjaxResult examineGoalList(@RequestBody ExamineGoalDto examineGoalDto) {
        return AjaxResult.success(goalExtendService.examineGoalList(SecurityUtils.getLoginUser().getSysUser(), examineGoalDto));
    }

    /**
     * 我的代表-计划类别选择
     * 查询下级的计划数和计划通过数
     *
     * @param type 0:年计划/1:月计划
     */
    @GetMapping("/juniorGoalNumber")
    public AjaxResult juniorGoalNumber(@Valid @NotNull(message = "计划类型不能为空!") @RequestParam int type) {
        return AjaxResult.success(goalExtendService.juniorGoalNumber(SecurityUtils.getLoginUser().getSysUser(), type));
    }

    /**
     * 提交团队计划
     *
     * @param type 0:年计划/1:月计划
     */
    @GetMapping("/submitTeamGoal")
    public AjaxResult submitTeamGoal(@Valid @NotNull(message = "计划类型不能为空!") @RequestParam int type) {
        return AjaxResult.success(goalExtendService.submitTeamGoal(SecurityUtils.getLoginUser().getSysUser(), type));
    }

    /**
     * 定时任务每月去下发周计划
     */
    @InnerAuth
    @GetMapping("/weeksGoal")
    public void weeksGoal() {
        goalExtendService.weeksGoal();
    }

    /**
     * 定时任务每年去下发月计划
     */
    @InnerAuth
    @GetMapping("/yearsGoal")
    public void yearsGoal() {
        goalExtendService.yearsGoal();
    }

    /**
     * 查询分组下发字段
     */
    @InnerAuth
    @PostMapping("/selGoalGroupFieldsList")
    public R<List<GoalGroupFields>> selGoalGroupFieldsList(@RequestBody GoalGroupFields groupFields) {
        return R.ok(goalExtendService.selGoalGroupFieldsList(groupFields));
    }

    /**
     * 查询目标字段
     */
    @InnerAuth
    @PostMapping("/selGoalFields")
    public R<List<GoalFields>> selGoalFields(@RequestBody GoalFields fields) {
        return R.ok(goalExtendService.selGoalFields(fields));
    }


    /**
     * 统计月计划年计划招聘数
     * @return
     */
    @PostMapping("/getPlanCount")
    @InnerAuth
    public AjaxResult getPlanCount() {
        return AjaxResult.success(goalExtendService.getPlanCountVo());
    }

}
