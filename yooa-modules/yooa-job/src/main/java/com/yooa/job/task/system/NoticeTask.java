package com.yooa.job.task.system;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.system.api.RemoteNoticeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component("noticeTask")
public class NoticeTask {

    private final RemoteNoticeService remoteNoticeService;

    /**
     * 定时发送通知
     */
    public void sendNoticeByTiming() {
        R<Integer> r = remoteNoticeService.sendNoticeByTiming(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("定时发送通知定时任务异常：" + r.getMsg());
        }
    }

    /**
     * 同步通知状态
     */
    public void syncNoticeStatus() {
        R<Integer> r = remoteNoticeService.syncNoticeStatus(SecurityConstants.INNER);
        if (r.getCode() != R.SUCCESS) {
            throw new ServiceException("同步通知状态定时任务异常：" + r.getMsg());
        }
    }


}
