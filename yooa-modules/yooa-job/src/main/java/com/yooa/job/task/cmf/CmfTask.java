package com.yooa.job.task.cmf;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import com.yooa.cmf.api.RemoteCmfAdminService;
import com.yooa.cmf.api.RemoteCmfExtendService;
import com.yooa.cmf.api.domain.CmfAgentAdmin;
import com.yooa.cmf.api.domain.dto.DataCollectDto;
import com.yooa.cmf.api.domain.vo.ExtendTargetProgressVo;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.crm.api.RemoteCustomerOrderService;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.CrmCustomerUserBindRecord;
import com.yooa.extend.api.RemoteExtendService;
import com.yooa.extend.api.domain.ExtendTargetProgress;
import com.yooa.extend.api.domain.ExtendTargetProgressHour;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component("cmfTask")
@AllArgsConstructor
public class CmfTask {

    private final RemoteCmfAdminService remoteCmfAdminService;
    private final RemoteCmfExtendService remoteCmfExtendService;
    private final RemoteExtendService remoteExtendService;
    private final RemoteCustomerOrderService remoteOrderService;

    /**
     * 每天9点,去查昨天的数据然后把结果存到我们数据库
     * 事务锁和数据库切换不能一起用
     */
    public void dataTaskAdd() {
        // 拿到数据汇总最后统计的时间
        ExtendTargetProgress extendTargetProgress = remoteExtendService.finallyOne(SecurityConstants.INNER).getData();

        // 要新增的时间集
        List<LocalDate> dateList = CollUtil.newArrayList();
        LocalDate beginDate = null;     // 起始时间
        LocalDate endDate = LocalDate.now().minusDays(1);       // 结束时间

        if (ObjectUtils.isEmpty(extendTargetProgress)) {
            // 为空从今年年初开始,昨天结束
            beginDate = LocalDate.of(2024, 1, 1);
        }
        else {
            // 不为空从数据汇总最后的时间+1,到昨天(不能统计今天的数据)
            if (extendTargetProgress.getDate()
                    .compareTo(LocalDate.now().minusDays(1)) != 0) {        // 如果数据汇总最后一天数据为昨天就不需要统计了
                beginDate = extendTargetProgress.getDate().plusDays(1);
            }
            else {
                beginDate = LocalDate.now();
            }
        }

        dateList = LocalDateUtil.getDatesBetween(beginDate, endDate);         // 获取时间差的每一天

        // 汇总每一天的数据
        dateList.forEach(d -> {
            LocalDateTime beginTime = d.atStartOfDay();                         // 起始时间
            LocalDateTime endTime = d.plusDays(1).atStartOfDay();     // 结束时间(起始时间+1)

            /**推广的分在职、离职查询方式(推广的在职离职的业数据统计方式不一样)*/
            List<ExtendTargetProgressVo> extendList = remoteCmfExtendService.dataCollect1(DataCollectDto.builder()
                    .beginTime(beginTime)
                    .endTime(endTime)
                    .build(), SecurityConstants.INNER).getData();        // 推广的(在职)

            List<ExtendTargetProgressVo> extendList1 = remoteCmfExtendService.dataCollect1f(DataCollectDto.builder()
                    .beginTime(beginTime)
                    .endTime(endTime)
                    .build(), SecurityConstants.INNER).getData();        // 推广的(离职)

            List<ExtendTargetProgressVo> vipList = remoteCmfExtendService.dataCollect2(DataCollectDto.builder()
                    .beginTime(beginTime)
                    .endTime(endTime)
                    .build(), SecurityConstants.INNER).getData();        // vip的

            List<CrmCustomerOrder> extendOrderList = remoteOrderService.selOrderDayMoney(
                    new ArrayList<>(),
                    beginTime.toString(),
                    endTime.toString(),                                 //  推广的订单总业绩
                    0,
                    SecurityConstants.INNER).getData();
            List<CrmCustomerOrder> vipOrderList = remoteOrderService.selOrderDayMoney(
                    new ArrayList<>(),
                    beginTime.toString(),
                    endTime.toString(),                                 //  VIP的订单总业绩
                    1,
                    SecurityConstants.INNER).getData();

            List<CrmCustomerOrder> extendNewOrderList = remoteOrderService.selOrderDayNewMoney(
                    new ArrayList<>(),
                    beginTime.toString(),
                    endTime.toString(),                                 //  推广的订单新增业绩
                    0,
                    SecurityConstants.INNER).getData();
            List<CrmCustomerOrder> vipNewOrderList = remoteOrderService.selOrderDayNewMoney(
                    new ArrayList<>(),
                    beginTime.toString(),
                    endTime.toString(),                                 //  VIP的订单新增业绩
                    1,
                    SecurityConstants.INNER).getData();

            // 插入结果
            extendList.forEach(l -> {     // 推广(在职)
                extendOrderList.forEach(e -> {
                    if (l.getAgentAdminId() == e.getPyExtendId().intValue()) {
                        l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                    }
                });

                extendNewOrderList.forEach(e -> {
                    if (l.getAgentAdminId() == e.getPyExtendId().intValue()) {
                        l.setFortyFiveDays(e.getOrderMoney());    // 新增业绩
                    }
                });
                l.setDate(beginTime.toLocalDate());
            });

            extendList1.forEach(l -> {    // 推广(离职)
                extendOrderList.forEach(e -> {
                    if (l.getAgentAdminId() == e.getPyExtendId().intValue()) {
                        l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                    }
                });
                l.setDate(beginTime.toLocalDate());
                extendList.add(l);
            });


            vipList.forEach(l -> {    // VIP
                vipOrderList.forEach(e -> {
                    if (l.getAgentAdminId() == e.getPyServeId().intValue()) {
                        l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                    }
                });

                vipNewOrderList.forEach(e -> {
                    if (l.getAgentAdminId() == e.getPyServeId().intValue()) {
                        l.setFortyFiveDays(e.getOrderMoney());    // 新增业绩
                    }
                });

                l.setDate(beginTime.toLocalDate());
                extendList.add(l);
            });

            if (extendList.size() > 0) {
                List<ExtendTargetProgress> extendTargetProgressList = new ArrayList<>();
                extendList.forEach(extendTargetProgressVo -> {
                    ExtendTargetProgress copyExtendTargetProgress = new ExtendTargetProgress();
                    BeanUtils.copyProperties(extendTargetProgressVo, copyExtendTargetProgress);
                    extendTargetProgressList.add(copyExtendTargetProgress);
                });
                insertAll(extendTargetProgressList);
            }
        });
    }

    /**
     * 当天每小时去查一遍小时数据(保存两天)
     * 事务锁和数据库切换不能一起用
     */
    public void dataTaskAddHour() {
        LocalDateTime beginTime = LocalDate.now().atStartOfDay();                                           // 起始时间(当天零点)
        LocalDateTime endTime = LocalDate.now().atStartOfDay().withHour(LocalDateTime.now().getHour());     // 结束时间(当前时间小时整点)

        /**推广的分在职、离职查询方式(推广的在职离职的业数据统计方式不一样)*/
        List<ExtendTargetProgressVo> list = remoteCmfExtendService.dataCollect1(DataCollectDto.builder()
                .beginTime(beginTime)
                .endTime(endTime)
                .build(), SecurityConstants.INNER).getData();        // 推广的(在职)

        List<ExtendTargetProgressVo> list2 = remoteCmfExtendService.dataCollect1f(DataCollectDto.builder()
                .beginTime(beginTime)
                .endTime(endTime)
                .build(), SecurityConstants.INNER).getData();        // 推广的(离职)

        List<ExtendTargetProgressVo> list1 = remoteCmfExtendService.dataCollect2(DataCollectDto.builder()
                .beginTime(beginTime)
                .endTime(endTime)
                .build(), SecurityConstants.INNER).getData();        // vip的

        List<CrmCustomerOrder> extendOrderList = remoteOrderService.selOrderDayMoney(
                new ArrayList<>(),
                beginTime.toString(),
                endTime.toString(),                                 //  推广的订单业绩
                0,
                SecurityConstants.INNER).getData();
        List<CrmCustomerOrder> vipOrderList = remoteOrderService.selOrderDayMoney(
                new ArrayList<>(),
                beginTime.toString(),
                endTime.toString(),                                 //  VIP的订单业绩
                1,
                SecurityConstants.INNER).getData();

        // 插入结果
        list.forEach(l -> {     // 推广(在职)
            extendOrderList.forEach(e -> {
                if (l.getAgentAdminId() == e.getPyExtendId().intValue()) {
                    l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                }
            });
            l.setDate(beginTime.toLocalDate());
        });

        list2.forEach(l -> {    // 推广(离职)
            extendOrderList.forEach(e -> {
                if (l.getAgentAdminId() == e.getPyExtendId().intValue()) {
                    l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                }
            });
            l.setDate(beginTime.toLocalDate());
            list.add(l);
        });

        list1.forEach(l -> {    // VIP
            vipOrderList.forEach(e -> {
                if (l.getAgentAdminId() == e.getPyServeId().intValue()) {
                    l.setRealProfit(e.getOrderMoney());       // 汇总业绩
                }
            });
            l.setDate(beginTime.toLocalDate());
            list.add(l);
        });

        if (list.size() > 0) {
            List<ExtendTargetProgressHour> hours = new ArrayList<>();
            list.forEach(extendTargetProgressVo -> {
                ExtendTargetProgressHour hour = new ExtendTargetProgressHour();
                BeanUtils.copyProperties(extendTargetProgressVo, hour);
                hour.setDate(endTime);
                hours.add(hour);
            });
            insertAllHour(hours);
        }
    }

    /**
     * 每天零点执行一次,删除超过2天前的每小时数据汇总
     * 事务锁和数据库切换不能一起用
     */
    public void dataTaskDeleteHour() {
        remoteExtendService.deleteTargetProgressHour(SecurityConstants.INNER);
    }


    /**
     * 重新计算每日数据汇总(改绑的人参与重新计算)
     */
    @Transactional(rollbackFor = Exception.class)
    public CrmCustomerUserBindRecord pdRebinding(CrmCustomerUserBindRecord bindRecord) {
        // 成功走完,就算处理成功
        try {
            Integer types1 = null;
            Integer types2 = null;

            if (ObjUtil.isNotNull(bindRecord.getAfterPdUserId()) && bindRecord.getAfterPdUserId() != 0) {
                CmfAgentAdmin afterAgentAdmin = remoteCmfAdminService
                        .getById(bindRecord.getAfterPdUserId(), SecurityConstants.INNER)
                        .getData();

                if (ObjUtil.isEmpty(afterAgentAdmin)) {
                    throw new ServiceException("查询改绑记录-改绑后的ID为{" + bindRecord.getAfterPdUserId() + "}的PD用户为NULL");
                }
                types1 = afterAgentAdmin.getTypes();
            }

            if (ObjUtil.isNotNull(bindRecord.getBeforePdUserId()) && bindRecord.getBeforePdUserId() != 0) {
                CmfAgentAdmin beforeAgentAdmin = remoteCmfAdminService
                        .getById(bindRecord.getBeforePdUserId(), SecurityConstants.INNER)
                        .getData();

                if (ObjUtil.isEmpty(beforeAgentAdmin)) {
                    throw new ServiceException("查询改绑记录-改绑前的ID为{" + bindRecord.getBeforePdUserId() + "}的PD用户为NULL");
                }
                types2 = beforeAgentAdmin.getTypes();
            }
            /**上面方法为查找pd用户类型(推广/VIP)**/

            LocalDate beginTime = LocalDateTimeUtil.of(bindRecord.getBindTime()).toLocalDate();
            LocalDate endTime = LocalDateTimeUtil.of(bindRecord.getBindTime()).plusDays(1).toLocalDate();

            if (bindRecord.getType() == 1) {     // 用户需要修改几天的(从改绑时间到昨天)
                endTime = LocalDate.now().minusDays(1);
            }// 订单只需要修改,改绑那天的
            List<LocalDate> dateList = LocalDateUtil.getDatesBetween(beginTime, endTime);

            List<ExtendTargetProgressVo> list = new ArrayList<>();        // 改绑人的历史汇总数据
            List<ExtendTargetProgressVo> list2 = new ArrayList<>();       // 被改绑人的历史汇总数据
            for (LocalDate d : dateList) {
                if (ObjUtil.isNotNull(types1)) {
                    if (types1 == 0) {       // 改绑人
                        List<ExtendTargetProgressVo> voList = remoteCmfExtendService.dataCollect1(DataCollectDto.builder()
                                .id(bindRecord.getAfterPdUserId())
                                .beginTime(d.atStartOfDay())
                                .endTime(d.plusDays(1).atStartOfDay())
                                .build(), SecurityConstants.INNER).getData();       // 推广的

                        if (CollUtil.isNotEmpty(voList)) {
                            ExtendTargetProgressVo vo = voList.get(0);
                            vo.setDate(d);
                            list.add(vo);
                        }
                    }
                    else if (types1 == 1) {
                        List<ExtendTargetProgressVo> voList = remoteCmfExtendService.dataCollect2(DataCollectDto.builder()
                                .id(bindRecord.getAfterPdUserId())
                                .beginTime(d.atStartOfDay())
                                .endTime(d.plusDays(1).atStartOfDay())
                                .build(), SecurityConstants.INNER).getData();       // vip的

                        if (CollUtil.isNotEmpty(voList)) {
                            ExtendTargetProgressVo vo = voList.get(0);
                            vo.setDate(d);
                            list.add(vo);
                        }
                    }
                }

                if (ObjUtil.isNotNull(types2)) {     // 被改绑人
                    if (types2 == 0) {
                        List<ExtendTargetProgressVo> voList = remoteCmfExtendService.dataCollect1(DataCollectDto.builder()
                                .id(bindRecord.getBeforePdUserId())
                                .beginTime(d.atStartOfDay())
                                .endTime(d.plusDays(1).atStartOfDay())
                                .build(), SecurityConstants.INNER).getData();       // 推广的

                        if (CollUtil.isNotEmpty(voList)) {
                            ExtendTargetProgressVo vo = voList.get(0);
                            vo.setDate(d);
                            list2.add(vo);
                        }
                    }
                    else if (types2 == 1) {
                        List<ExtendTargetProgressVo> voList = remoteCmfExtendService.dataCollect2(DataCollectDto.builder()
                                .id(bindRecord.getBeforePdUserId())
                                .beginTime(d.atStartOfDay())
                                .endTime(d.plusDays(1).atStartOfDay())
                                .build(), SecurityConstants.INNER).getData();      // vip的

                        if (CollUtil.isNotEmpty(voList)) {
                            ExtendTargetProgressVo vo = voList.get(0);
                            vo.setDate(d);
                            list2.add(vo);
                        }
                    }
                }

            }
            if (CollUtil.isNotEmpty(list)) {
                List<CrmCustomerOrder> afterOrderList = CollUtil.newArrayList();        // 改版人

                if (types1 == 0) {      // 改版人
                    afterOrderList = remoteOrderService.selOrderDayMoney(
                            CollUtil.newArrayList(bindRecord.getAfterPdUserId()),
                            LocalDateTimeUtil.of(bindRecord.getBindTime()).toString(),
                            null,
                            0,
                            SecurityConstants.INNER).getData();
                }
                else if (types1 == 1) {
                    afterOrderList = remoteOrderService.selOrderDayMoney(
                            CollUtil.newArrayList(bindRecord.getAfterPdUserId()),
                            LocalDateTimeUtil.of(bindRecord.getBindTime()).toString(),
                            null,
                            1,
                            SecurityConstants.INNER).getData();
                }
                for (ExtendTargetProgressVo l : list) {
                    if (CollUtil.isNotEmpty(afterOrderList)) {
                        BigDecimal big = afterOrderList.stream()
                                .filter(a -> a.getOrderTime().compareTo(LocalDateTimeUtil.of(l.getDate())) == 0)
                                .map(CrmCustomerOrder::getOrderMoney)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        l.setRealProfit(big);         // 查业绩
                    }
                    else {
                        l.setRealProfit(BigDecimal.ZERO);
                    }
                }
                updateAll(list);        // 修改集合
            }
            if (CollUtil.isNotEmpty(list2)) {
                List<CrmCustomerOrder> beforeOrderList = CollUtil.newArrayList();       // 被版人

                if (types2 == 0) {      // 被版人
                    beforeOrderList = remoteOrderService.selOrderDayMoney(
                            CollUtil.newArrayList(bindRecord.getBeforePdUserId()),
                            LocalDateTimeUtil.of(bindRecord.getBindTime()).toString(),
                            null,
                            0,
                            SecurityConstants.INNER).getData();
                }
                else if (types2 == 1) {
                    beforeOrderList = remoteOrderService.selOrderDayMoney(
                            CollUtil.newArrayList(bindRecord.getBeforePdUserId()),
                            LocalDateTimeUtil.of(bindRecord.getBindTime()).toString(),
                            null,
                            1,
                            SecurityConstants.INNER).getData();
                }
                for (ExtendTargetProgressVo l : list2) {
                    if (CollUtil.isNotEmpty(beforeOrderList)) {
                        BigDecimal big = beforeOrderList.stream()
                                .filter(a -> a.getOrderTime().compareTo(LocalDateTimeUtil.of(l.getDate())) == 0)
                                .map(CrmCustomerOrder::getOrderMoney)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        l.setRealProfit(big);         // 查业绩
                    }
                    else {
                        l.setRealProfit(BigDecimal.ZERO);
                    }
                }
                updateAll(list2);         // 修改集合
            }
            bindRecord.setStatus(1);            // 处理成功,修改状态为已处理
            return bindRecord;
        }
        catch (Exception e) {
            bindRecord.setStatus(0);            // 处理失败,修改状态为未处理
            return bindRecord;
        }
    }

    /**
     * 事务锁和数据库切换不能一起用
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertAll(List<ExtendTargetProgress> list) {
        List<List<ExtendTargetProgress>> listList = new ArrayList<>();
        if (list.size() > 1000) {           // 数据量太大分割成多个小集合
            listList = IntStream.range(0, list.size())
                    .boxed()
                    .collect(Collectors.groupingBy(index -> index / 300))
                    .values()
                    .stream()
                    .map(indices -> indices.stream().map(list::get).collect(Collectors.toList()))
                    .collect(Collectors.toList());
        }

        if (listList.size() > 0) {
            listList.forEach(l -> {       // 分批次批量插入
                remoteExtendService.insertAllTargetProgress(l, SecurityConstants.INNER);
            });
        }
        else if (list.size() > 0) {
            remoteExtendService.insertAllTargetProgress(list, SecurityConstants.INNER);
        }
    }

    /**
     * 事务锁和数据库切换不能一起用
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAll(List<ExtendTargetProgressVo> list) {
        if (list.size() > 0) {
            List<ExtendTargetProgress> extendTargetProgressList = new ArrayList<>();
            list.forEach(l -> {
                ExtendTargetProgress extendTargetProgress = new ExtendTargetProgress();
                BeanUtils.copyProperties(l, extendTargetProgress);
                extendTargetProgressList.add(extendTargetProgress);
            });
            remoteExtendService.updateAllTargetProgress(extendTargetProgressList, SecurityConstants.INNER);
        }
    }

    /**
     * 事务锁和数据库切换不能一起用
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertAllHour(List<ExtendTargetProgressHour> list) {
        List<List<ExtendTargetProgressHour>> listList = new ArrayList<>();
        if (list.size() > 1000) {           // 数据量太大分割成多个小集合
            listList = IntStream.range(0, list.size())
                    .boxed()
                    .collect(Collectors.groupingBy(index -> index / 300))
                    .values()
                    .stream()
                    .map(indices -> indices.stream().map(list::get).collect(Collectors.toList()))
                    .collect(Collectors.toList());
        }

        if (listList.size() > 0) {
            listList.forEach(l -> {       // 分批次批量插入
                remoteExtendService.insertAllTargetProgressHour(l, SecurityConstants.INNER);
            });
        }
        else if (list.size() > 0) {
            remoteExtendService.insertAllTargetProgressHour(list, SecurityConstants.INNER);
        }
    }

}
