<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.cmf.mapper.CmfAgentAdminMapper">

    <resultMap id="BaseResultMap" type="com.yooa.cmf.api.domain.CmfAgentAdmin">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="salt" column="salt" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
        <result property="adminRole" column="admin_role" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="INTEGER"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="loginfailure" column="loginfailure" jdbcType="TINYINT"/>
        <result property="logintime" column="logintime" jdbcType="INTEGER"/>
        <result property="loginip" column="loginip" jdbcType="VARCHAR"/>
        <result property="token" column="token" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="weigh" column="weigh" jdbcType="TINYINT"/>
        <result property="usersId" column="users_id" jdbcType="INTEGER"/>
        <result property="hot" column="hot" jdbcType="INTEGER"/>
        <result property="invitationCode" column="invitation_code" jdbcType="VARCHAR"/>
        <result property="types" column="types" jdbcType="TINYINT"/>
    </resultMap>

</mapper>