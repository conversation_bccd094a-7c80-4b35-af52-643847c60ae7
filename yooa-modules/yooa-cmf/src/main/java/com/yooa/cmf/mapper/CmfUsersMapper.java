package com.yooa.cmf.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.cmf.api.domain.CmfUsers;
import com.yooa.cmf.api.domain.vo.ConsumerUpVo;
import com.yooa.cmf.api.domain.vo.ConvertAnchorVo;
import com.yooa.cmf.api.domain.vo.ConvertCustomerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户_运营_主播 - 数据层
 */
public interface CmfUsersMapper extends BaseMapper<CmfUsers> {

    List<ConsumerUpVo> totalUp(@Param("ids") List<Long> ids);

    List<ConsumerUpVo> consumerUpChoose(@Param("ids") List<Long> ids, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 查询大于自增ID后的客户数据
     */
    List<ConvertCustomerVo> selectCustomersAfterId(@Param("id") Long id);

    /**
     * 查询大于最后登录时间后的客户数据
     */
    List<ConvertCustomerVo> selectCustomersAfterLastLoginTime(@Param("lastLoginTime") String lastLoginTime);

    /**
     * 查询大于自增ID后的主播数据
     */
    List<ConvertAnchorVo> selectAnchorListByGtId(@Param("id") Long id);
}




