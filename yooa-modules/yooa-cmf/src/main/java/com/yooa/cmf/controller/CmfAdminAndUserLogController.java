package com.yooa.cmf.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.cmf.api.domain.CmfAgentAdminAndUserLog;
import com.yooa.cmf.service.CmfAdminAndUserLogService;
import com.yooa.common.core.domain.R;
import com.yooa.common.security.annotation.InnerAuth;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 推广用户关联记录 - 控制层
 *
 * <AUTHOR>
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/cmf/admin/user/log")
public class CmfAdminAndUserLogController {

    private final CmfAdminAndUserLogService cmfAdminAndUserLogService;

    @InnerAuth
    @RequestMapping("/list/{customerId}")
    public R<List<CmfAgentAdminAndUserLog>> listByCustomerId(@PathVariable Long customerId) {
        List<CmfAgentAdminAndUserLog> list = cmfAdminAndUserLogService.list(
                Wrappers.<CmfAgentAdminAndUserLog>lambdaQuery()
                        .eq(CmfAgentAdminAndUserLog::getUserId, customerId)
                        .eq(CmfAgentAdminAndUserLog::getStatus, 1)
        );
        return R.ok(list);
    }

    @InnerAuth
    @RequestMapping("/last-info/{customerId}")
    public R<CmfAgentAdminAndUserLog> lastInfoByCustomerId(@PathVariable Long customerId) {
        CmfAgentAdminAndUserLog info = cmfAdminAndUserLogService.getOne(
                Wrappers.<CmfAgentAdminAndUserLog>lambdaQuery()
                        .eq(CmfAgentAdminAndUserLog::getUserId, customerId)
                        .eq(CmfAgentAdminAndUserLog::getStatus, 1)
                        .orderByDesc(CmfAgentAdminAndUserLog::getId)
                        .last("limit 1")
        );
        return R.ok(info);
    }
}
