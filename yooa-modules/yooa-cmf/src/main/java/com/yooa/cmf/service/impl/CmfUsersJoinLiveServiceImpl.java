package com.yooa.cmf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.domain.CmfUsersJoinLive;
import com.yooa.cmf.api.domain.vo.ConvertCustomerJoinAnchorVo;
import com.yooa.cmf.mapper.CmfUsersJoinLiveMapper;
import com.yooa.cmf.service.CmfUsersJoinLiveService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CmfUsersJoinLiveServiceImpl extends ServiceImpl<CmfUsersJoinLiveMapper, CmfUsersJoinLive> implements CmfUsersJoinLiveService {

    @Override
    public List<ConvertCustomerJoinAnchorVo> listByGtId(Long id) {
        return baseMapper.selectListByGtId(id);
    }

    @Override
    public List<ConvertCustomerJoinAnchorVo> listByStatusChange(List<Long> ids) {
        return baseMapper.selectListByStatusChange(ids);
    }
}
