package com.yooa.cmf.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.domain.CmfAgentAdminAndUser;
import com.yooa.cmf.mapper.CmfAgentAdminAndUserMapper;
import com.yooa.cmf.service.CmfAdminAndUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 推广用户关联 - 服务实现层
 */
@AllArgsConstructor
@Service
public class CmfAdminAndUserServiceImpl extends ServiceImpl<CmfAgentAdminAndUserMapper, CmfAgentAdminAndUser> implements CmfAdminAndUserService {


}
