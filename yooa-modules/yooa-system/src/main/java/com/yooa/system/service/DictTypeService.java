package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.SysDictType;
import com.yooa.system.api.domain.query.DictTypeQuery;

import java.util.List;

/**
 * 数据字典类型 - 服务层
 */
public interface DictTypeService extends IService<SysDictType> {

    /**
     * 根据条件分页查询字典类型
     */
    public List<SysDictType> selectDictTypeList(Page<SysDictType> page, DictTypeQuery query);

    /**
     * 根据字典类型查询字典数据
     */
    public List<SysDictData> selectDictDataByType(String dictType);

    /**
     * 批量删除字典信息
     */
    public void deleteDictTypeByIds(List<Long> dictIds);

    /**
     * 加载字典缓存数据
     */
    public void loadingDictCache();

    /**
     * 清空字典缓存数据
     */
    public void clearDictCache();

    /**
     * 重置字典缓存数据
     */
    public void resetDictCache();

    /**
     * 新增保存字典类型信息
     */
    public int insertDictType(SysDictType dictType);

    /**
     * 修改保存字典类型信息
     */
    public int updateDictType(SysDictType dictType);

    /**
     * 校验字典类型称是否唯一
     */
    public boolean checkDictTypeUnique(SysDictType dictType);
}
