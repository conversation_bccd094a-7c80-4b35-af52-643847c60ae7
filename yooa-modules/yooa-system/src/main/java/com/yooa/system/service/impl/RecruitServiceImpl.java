package com.yooa.system.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.*;

import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.query.InterviewQuery;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.*;
import com.yooa.system.domain.vo.TreeSelect;
import com.yooa.system.mapper.*;

import com.yooa.system.service.DeptService;
import com.yooa.system.service.RecruitService;
import lombok.AllArgsConstructor;
import org.apache.catalina.security.SecurityUtil;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.naming.NamingException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 招聘计划业务层处理
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class RecruitServiceImpl extends ServiceImpl<SysRecruitMapper, SysRecruit> implements RecruitService {
    private final SysRosterUserMapper sysRosterUserMapper;
    private final SysRosterMapper sysRosterMapper;
    private final SysDeptMapper sysDeptMapper;
    private final SysUserMapper sysUserMapper;
    private final SysRecruitMapper sysRecruitMapper;
    private final SysRecruitCountMapper sysRecruitCountMapper;
    private final SysProcessBacklogMapper sysProcessBacklogMapper;
    private final SysRecruitUserMapper sysRecruitUserMapper;
    private final SysRecruitOperateMapper sysRecruitOperateMapper;
    private final SysRecruitUserSubmitMapper sysRecruitUserSubmitMapper;
    private final SysInterviewMapper sysInterviewMapper;
    private final DeptService deptService;
    /**
     * 分页查询
     * @return 招聘计划集合信息
     */
    @Override
    public List<SysRecruitVo> selectRecruitList(Page<SysRecruitVo> page,SysRecruitVo vo) {
        return baseMapper.selectRecruitList(page,vo);
    }

    /**
     * @return 查询招聘计划 属于同一个流程的一起返回
     */
    @Override
    public List<HashMap<String, Object>> getAllRecruitList(SysRecruitVo vo) {
        List<HashMap<String, Object>> list = new ArrayList<>();
        //先根据状态查询
        LambdaQueryWrapper<SysRecruit> wrapperRecruit = new LambdaQueryWrapper<>();
        wrapperRecruit.eq(SysRecruit::getState, vo.getState());
        wrapperRecruit.orderByDesc(SysRecruit::getCreateTime);
        List<SysRecruit> sysRecruits = this.baseMapper.selectList(wrapperRecruit);
        List<Long> pressId= new ArrayList<>();
        for (SysRecruit sysRecruit: sysRecruits) {
            HashMap<String,Object> recruitAllMap = new HashMap<>();
            //判断当前流程是否添加
            if (!pressId.contains(sysRecruit.getProcessId())) {
                //查询跟当前计划属于同一个流程的计划
                LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SysRecruit::getProcessId, sysRecruit.getProcessId());
                List<SysRecruit> sysRecruitList = sysRecruitMapper.selectList(wrapper);

                HashMap<Long, Object> recruitMap = new HashMap<>();

                pressId.add(sysRecruit.getProcessId());
                for (SysRecruit sysRecruit2: sysRecruitList) {
                    recruitMap.put(sysRecruit2.getId(), sysRecruit2);
                }
                //记录已添加的流程ID
                pressId.add(sysRecruit.getProcessId());
                recruitAllMap.put("recruitList",recruitMap);
                recruitAllMap.put("processId",sysRecruit.getProcessId());
                list.add(recruitAllMap);
            }
        }
        return list;
    }


    /**
     * 导入招聘数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public String importRecruit(MultipartFile file,String month) throws Exception {
        //先判断该人员这个月是否有提交，一个月只许提交一次
//        SysUser sysUser1=sysUserMapper.selectById(SecurityUtils.getUserId());
//        LambdaQueryWrapper<SysRecruitUser> wrapperUser = new LambdaQueryWrapper<>();
//        wrapperUser.eq(SysRecruitUser::getUserId, sysUser1.getUserId());
//        wrapperUser.like(SysRecruitUser::getCreateTime, month);
//        List<SysRecruitUser> sysRecruitUser1 = sysRecruitUserMapper.selectList(wrapperUser);
//        if(sysRecruitUser1.size()>0) {
//            throw new IllegalArgumentException("提交失败，该人员这个月已提交过计划");
//        }
        int failureNum = 0;
        int successNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        if (!file.isEmpty()) {
            //文件名称
            int begin = Objects.requireNonNull(file.getOriginalFilename()).indexOf(".");
            //文件名称长度
            int last = file.getOriginalFilename().length();
            //判断文件格式是否正确
            String fileName = file.getOriginalFilename().substring(begin, last);
            if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
                System.out.println("上传文件格式不正确,只支持xls、xlsx文件");
            }
        } else {
            throw new IllegalArgumentException("excel格式错误");
        }
        ImportParams importParams = new ImportParams();
        //表头(列字段占几行)
        importParams.setHeadRows(1);
        // 校验Excel文件，去掉空行
        importParams.setNeedVerify(true);
        //设置读取行数(默认从0开始)
        importParams.setReadRows(299);
        //流
        InputStream inputStream = file.getInputStream();
        //二.获取excel中的数据
        Workbook workbook = WorkbookFactory.create(inputStream);
        Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
        List<SysRecruit> sysRecruitList = new ArrayList<>();
        Long count=0L;
        Long commissionerTotal=0L;
        Long manageTotal=0L;
        String deptName=null;
        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue; // 跳过标题行
            SysRecruit sysRecruit=new SysRecruit();
            String demandDepartment=row.getCell(0).getStringCellValue();
            sysRecruit.setDemandDepartment(demandDepartment);
            LambdaQueryWrapper<SysDept> wrapperDept = new LambdaQueryWrapper<>();
            wrapperDept.eq(SysDept::getDeptName, demandDepartment);
            SysDept sysDept=sysDeptMapper.selectOne(wrapperDept);
            if (sysDept != null) {
                sysRecruit.setDeptId(sysDept.getDeptId());
                String ancestors=sysDept.getAncestors()+","+sysDept.getDeptId();
                String[] ancestorsArray = ancestors.split(",");
                //判断属于那个类型
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("88"))){
                    sysRecruit.setRecruitType(1);
                }
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("238"))){
                    sysRecruit.setRecruitType(1);
                }
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("351"))){
                    sysRecruit.setRecruitType(1);
                }
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("89"))){
                    sysRecruit.setRecruitType(2);
                }
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("90"))){
                    sysRecruit.setRecruitType(3);
                }
                if(Arrays.stream(ancestorsArray).map(String::trim).anyMatch(x->x.equals("235"))){
                    sysRecruit.setRecruitType(4);
                }
            }

            deptName=row.getCell(0).getStringCellValue();
            double doubleCommissioner = Double.parseDouble(String.valueOf(row.getCell(1).getNumericCellValue()));
            Long longCommissioner = Math.round(doubleCommissioner);
            double doubleManage = Double.parseDouble(String.valueOf(row.getCell(2).getNumericCellValue()));
            Long longManage = Math.round(doubleManage);
            sysRecruit.setCommissioner(longCommissioner);
            sysRecruit.setManage(longManage);
            sysRecruit.setState("0");
            sysRecruit.setMonth(month);
            sysRecruitList.add(sysRecruit);
            //计算本次导入的总数 专员总数，管理总数
            commissionerTotal=commissionerTotal+longCommissioner;
            manageTotal=manageTotal+longManage;

        }

        //1.业务流程表加入数据 只新增一条流程记录
        SysProcessBacklog processBacklog = new SysProcessBacklog();
        processBacklog.setTypeNameBig("计划");
        processBacklog.setTitle(deptName+month+"招聘计划");
        //发起人ID 发起人姓名
        processBacklog.setApplicant(SecurityUtils.getUsername());
        processBacklog.setApplicantId(SecurityUtils.getUserId());
        //审批人固定为人事行政部的负责人
        SysDept sysDept=deptService.getDeptById(90L);
        processBacklog.setApprover(sysDept.getLeaderName());
        processBacklog.setApproverId(sysDept.getLeaderId());
        processBacklog.setTypeName("招聘");
        processBacklog.setType("1");
        processBacklog.setState("1");
        sysProcessBacklogMapper.insert(processBacklog);



        //4.招聘计划数量统计表新增当前计划数据 只新增一条记录
        count=commissionerTotal+manageTotal;
        LambdaQueryWrapper<SysRecruitCount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRecruitCount::getMonth, month);
        SysRecruitCount sysRecruitCount = new SysRecruitCount();
        sysRecruitCount.setProcessId(processBacklog.getId());
        sysRecruitCount.setTotal(count);
        sysRecruitCount.setCommissionerTotal(commissionerTotal);
        sysRecruitCount.setManageTotal(manageTotal);
        sysRecruitCount.setCommissionerRemaining(commissionerTotal);
        sysRecruitCount.setManageRemaining(manageTotal);
        sysRecruitCount.setMonth(month);
        sysRecruitCountMapper.insert(sysRecruitCount);

        //3新增当前人员BP组所有账号正常的人员信息(新增一次)
        SysUser sysUser=sysUserMapper.selectById(SecurityUtils.getUserId());
        UserQuery query=new UserQuery();
        query.setDeptId(sysUser.getDeptId());
        query.setStatus("0");
        List<SysUserVo> getUserList=sysUserMapper.getUserList(query);
        for (SysUserVo sysUserVo: getUserList) {
            SysRecruitUser sysRecruitUser=new SysRecruitUser();
            sysRecruitUser.setUserId(sysUserVo.getUserId());
            sysRecruitUser.setName(sysUserVo.getUserName());
            sysRecruitUser.setHireData(month);
            sysRecruitUser.setProcessId(processBacklog.getId());
            sysRecruitUser.setDeptId(sysUser.getDeptId());
            sysRecruitUserMapper.insert(sysRecruitUser);
        }

        for (SysRecruit recruit: sysRecruitList) {
            try {
                //2.招聘计划表新增提交表记录关联流程
                recruit.setUserId(SecurityUtils.getUserId());
                recruit.setName(SecurityUtils.getUsername());
                recruit.setProcessId(processBacklog.getId());
                baseMapper.insert(recruit);

                //5.操作记录表新增记录
                SysRecruitVo recruitVo=new SysRecruitVo();
                BeanUtils.copyProperties(recruit, recruitVo);
                recruitVo.setOperationType("上传");
                SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
                sysRecruitOperateMapper.insert(sysRecruitOperate);

                successNum++;
            }catch (Exception e) {
                failureNum++;
                String msg = "<br/>第" + failureNum + "条导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }


        successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        return successMsg.toString();
    }

    /**
     * 批量修改招聘计划
     * @param vo
     * @return
     */
    @Override
    public String updateRecruit(SysRecruitVo vo){
        List<SysRecruit> recruitList=vo.getRecruitList();
        for (SysRecruit sysRecruit: recruitList) {
            try{
                SysRecruit sysRecruit1=baseMapper.selectById(sysRecruit.getId());
                if (sysRecruit1.getState().equals("1")) {
                    return "修改失败,已确认的不能修改！";
                }
                //修改统计表中数据
                //1查询同一流程下的所有计划，重新统计数量，修改统计表中的数据
                LambdaQueryWrapper<SysRecruit> wrapperRecruit = new LambdaQueryWrapper<>();
                wrapperRecruit.eq(SysRecruit::getProcessId, sysRecruit.getProcessId());
                List<SysRecruit> sysRecruitList=sysRecruitMapper.selectList(wrapperRecruit);
                Long count=0L;
                Long commissionerTotal=0L;
                Long manageTotal=0L;
                //计算时去除当前修改的这个改为当前修改的数量
                for (SysRecruit sysRecruit2 : sysRecruitList) {
                    if (!sysRecruit2.getId().equals(sysRecruit.getId())) {
                        commissionerTotal=commissionerTotal+sysRecruit2.getCommissioner();
                        manageTotal=manageTotal+sysRecruit2.getManage();
                    }
                }
                //加上当前修改的计划数量
                commissionerTotal=commissionerTotal+sysRecruit.getCommissioner();
                manageTotal=manageTotal+sysRecruit.getManage();
                count=commissionerTotal+manageTotal;
                LambdaQueryWrapper<SysRecruitCount> wrapperCount = new LambdaQueryWrapper<>();
                wrapperCount.eq(SysRecruitCount::getProcessId, sysRecruit.getProcessId());
                SysRecruitCount sysRecruitCount=sysRecruitCountMapper.selectOne(wrapperCount);
                sysRecruitCount.setTotal(count);
                sysRecruitCount.setCommissionerTotal(commissionerTotal);
                sysRecruitCount.setManageTotal(manageTotal);
                sysRecruitCount.setCommissionerRemaining(commissionerTotal);
                sysRecruitCount.setManageRemaining(manageTotal);
                sysRecruitCountMapper.updateById(sysRecruitCount);
                sysRecruitMapper.updateById(sysRecruit);

            }catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "修改成功！";
    }

    @Override
    public String updateRecruitOne(SysRecruitVo vo) throws Exception {
        SysRecruit sysRecruit=new SysRecruit();
        BeanUtils.copyProperties(vo, sysRecruit);
        baseMapper.updateById(sysRecruit);
        return "修改成功";
    }

    @Override
    public String editBatch(SysRecruitVo vo) throws Exception {
        List<Long> processIdList=vo.getProcessIdList();
        LambdaQueryWrapper<SysRecruit> wrapperRecruit = new LambdaQueryWrapper<>();
        wrapperRecruit.in(SysRecruit::getProcessId, processIdList);
        List<SysRecruit> sysRecruitList=sysRecruitMapper.selectList(wrapperRecruit);
        for (SysRecruit recruit : sysRecruitList) {
            recruit.setState(vo.getState());
            this.baseMapper.updateById(recruit);
        }
        return "修改成功";
    }


    /**
     * 退回招聘计划/流程状态改为驳回,招聘计划改为退回
     * @param recruit
     * @return
     */
    @Override
    public int deleteRecruitByIds(SysRecruitVo recruit) {
        //修改当前计划所属流程下所有计划状态
        //1.未确认，计划状态改为驳回，流程状态改为驳回,审批人修改为发起人
        //2.已确认，计划状态改为未定，流程状态改为审批中
        if (recruit.getState().equals("0")) {
            LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysRecruit::getProcessId, recruit.getProcessId());
            List<SysRecruit> sysRecruits = sysRecruitMapper.selectList(wrapper);
            for (SysRecruit sysRecruit1 : sysRecruits) {
                sysRecruit1.setState("2");
                sysRecruitMapper.updateById(sysRecruit1);

                //新增驳回记录
                SysRecruitVo recruitVo=new SysRecruitVo();
                BeanUtils.copyProperties(sysRecruit1, recruitVo);
                recruitVo.setOperationType("驳回");
                SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
                sysRecruitOperateMapper.insert(sysRecruitOperate);

            }
            SysProcessBacklog sysProcessBacklog=sysProcessBacklogMapper.selectById(recruit.getProcessId());
            sysProcessBacklog.setState("3");
            sysProcessBacklog.setApproverId(sysProcessBacklog.getApplicantId());
            sysProcessBacklog.setApprover(sysProcessBacklog.getApplicant());
            return sysProcessBacklogMapper.updateById(sysProcessBacklog);
        }
        if (recruit.getState().equals("1")) {
            LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysRecruit::getProcessId, recruit.getProcessId());
            List<SysRecruit> sysRecruits = sysRecruitMapper.selectList(wrapper);
            for (SysRecruit sysRecruit1 : sysRecruits) {
                sysRecruit1.setState("0");
                sysRecruitMapper.updateById(sysRecruit1);

                //新增退回记录
                SysRecruitVo recruitVo=new SysRecruitVo();
                BeanUtils.copyProperties(sysRecruit1, recruitVo);
                recruitVo.setOperationType("退回");
                SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
                sysRecruitOperateMapper.insert(sysRecruitOperate);
            }
            SysProcessBacklog sysProcessBacklog=sysProcessBacklogMapper.selectById(recruit.getProcessId());
            sysProcessBacklog.setState("1");
            sysProcessBacklog.setApproverId(sysProcessBacklog.getApplicantId());
            sysProcessBacklog.setApprover(sysProcessBacklog.getApplicant());
            return sysProcessBacklogMapper.updateById(sysProcessBacklog);
        }
        return 1;
    }

    /**
     * 追回
     * @param sysRecruit
     * @return
     */
    @Override
    public int recoverRecruitByIds(SysRecruitVo sysRecruit) {
        //修改流程状态为追回，审批人改为申请人
        SysProcessBacklog sysProcessBacklog=sysProcessBacklogMapper.selectById(sysRecruit.getProcessId());
        sysProcessBacklog.setState("4");
        sysProcessBacklog.setApproverId(sysProcessBacklog.getApplicantId());
        sysProcessBacklog.setApprover(sysProcessBacklog.getApplicant());
        sysProcessBacklogMapper.updateById(sysProcessBacklog);


        LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRecruit::getProcessId, sysRecruit.getProcessId());
        List<SysRecruit> sysRecruits = sysRecruitMapper.selectList(wrapper);
        for (SysRecruit sysRecruit1 : sysRecruits) {
            SysRecruitVo recruitVo=new SysRecruitVo();
            BeanUtils.copyProperties(sysRecruit1, recruitVo);
            recruitVo.setOperationType("追回");
            SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
            sysRecruitOperateMapper.insert(sysRecruitOperate);
        }
        return 1;
    }

    /**
     * 通过/招聘计划修改为确认
     * @param sysRecruit
     * @return
     */
    @Override
    public int passRecruit(SysRecruitVo sysRecruit) {
        //修改当前计划所属流程下所有计划状态修改为确认,流程状态修改为通过，添加审批通过记录
        LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRecruit::getProcessId, sysRecruit.getProcessId());
        List<SysRecruit> sysRecruits = sysRecruitMapper.selectList(wrapper);
        for (SysRecruit sysRecruit1 : sysRecruits) {
            sysRecruit1.setState("1");
            sysRecruitMapper.updateById(sysRecruit1);

            SysRecruitVo recruitVo=new SysRecruitVo();
            BeanUtils.copyProperties(sysRecruit1, recruitVo);
            recruitVo.setOperationType("通过");
            SysRecruitOperate sysRecruitOperate=setProperty(recruitVo);
            sysRecruitOperateMapper.insert(sysRecruitOperate);
        }
        //修改流程状态为审批通过
        SysProcessBacklog sysProcessBacklog=sysProcessBacklogMapper.selectById(sysRecruit.getProcessId());
        sysProcessBacklog.setState("2");
        return sysProcessBacklogMapper.updateById(sysProcessBacklog);
    }

    /**
     * 统计各个部门需求岗位
     * @param page
     * @return
     */
    @Override
    public List<SysRecruitVo> getRecruitDeptCount(Page<SysRecruitVo> page,@Param("query") SysRecruitVo vo) {
       return baseMapper.getRecruitDeptCount(page,vo);
    }


    /**
     * 根据月份统计我的招聘计划
     * @return
     */
    @Override
    public SysRecruitVo getRecruitByMouthCount(SysRecruitVo vo) {
        String month=vo.getMonth();
        //统计本月总数，管理总数，专员总数
        LambdaQueryWrapper<SysRecruit> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(SysRecruit::getMonth, month);
        List<SysRecruit> sysRecruits = sysRecruitMapper.selectList(wrapper);
        Long commissioner=0L;
        Long manage=0L;

        for (SysRecruit sysRecruit : sysRecruits) {
            if (sysRecruit.getCommissioner()!=null){
                commissioner=commissioner+sysRecruit.getCommissioner();
            }
            if (sysRecruit.getManage()!=null){
                manage=manage+sysRecruit.getManage();
            }
        }
        Long total=commissioner+manage;
        vo.setTotal(total);
        vo.setSumCommissioner(commissioner);
        vo.setSumManage(manage);
        //本月入职员工总数
        InterviewQuery query2=new InterviewQuery();
        query2.setInterviewRole("5");
        query2.setEmploymentDate(month);
        List<SysRoster> listYGZS=sysInterviewMapper.selectThisMonthList(query2);
        vo.setSumCommissionerPlan(listYGZS.size());
        //本月入职管理总数
        InterviewQuery query3=new InterviewQuery();
        query3.setInterviewRole("2");
        query3.setEmploymentDate(month);
        List<SysRoster> listGLZS=sysInterviewMapper.selectThisMonthList(query3);
        vo.setSumManagePlan(listGLZS.size());
        //本月我的  入职员工数
        InterviewQuery query=new InterviewQuery();
        query.setInviteUserId(SecurityUtils.getUserId());
        query.setInterviewRole("5");
        query.setEmploymentDate(month);
        List<SysRoster> listYG=sysInterviewMapper.selectThisMonthList(query);
        vo.setMySumCommissionerPlan(listYG.size());
        //我的入职管理数量
        InterviewQuery query1=new InterviewQuery();
        query1.setInviteUserId(SecurityUtils.getUserId());
        query1.setInterviewRole("2");
        query.setEmploymentDate(month);
        List<SysRoster> listGL=sysInterviewMapper.selectThisMonthList(query1);
        vo.setMySumManagePlan(listGL.size());

       // CompletableFuture<List<SysRecruitUserSubmit>> listCompletableFuture = CompletableFuture.supplyAsync(() -> sysRecruitUserSubmitMapper.selectList(wrapperTg), executorService);

        //查询本月入职的人 入职的部门是推广和VIP,运营，职能，平台的数量
        //推广,VIP，市场入职数
        List<SysRoster> sysRosterListTGVIP=new ArrayList<>();
        RosterQuery deptQuery1=new RosterQuery();
        deptQuery1.setEmploymentDate(month);
        deptQuery1.setDeptId(88L);
        deptQuery1.setInviteUerId(SecurityUtils.getUserId());
        List<SysRoster> listTG=sysRosterMapper.selectThisMonthList(deptQuery1);

        RosterQuery deptQuery2=new RosterQuery();
        deptQuery2.setEmploymentDate(month);
        deptQuery2.setDeptId(238L);
        deptQuery2.setInviteUerId(SecurityUtils.getUserId());
        List<SysRoster> listVIP=sysRosterMapper.selectThisMonthList(deptQuery2);

        RosterQuery deptQueryS=new RosterQuery();
        deptQueryS.setEmploymentDate(month);
        deptQueryS.setDeptId(351L);
        deptQueryS.setInviteUerId(SecurityUtils.getUserId());
        List<SysRoster> listSC=sysRosterMapper.selectThisMonthList(deptQueryS);
        sysRosterListTGVIP.addAll(listTG);
        sysRosterListTGVIP.addAll(listVIP);
        sysRosterListTGVIP.addAll(listSC);

        //运营入职数
        List<SysRoster> sysRosterListYY=new ArrayList<>();
        RosterQuery deptQuery3=new RosterQuery();
        deptQuery3.setEmploymentDate(month);
        deptQuery3.setDeptId(89L);
        deptQuery3.setInviteUerId(SecurityUtils.getUserId());
        sysRosterListYY=sysRosterMapper.selectThisMonthList(deptQuery3);

        //职能入职数(人事行政部)
        List<SysRoster> sysRosterListZN=new ArrayList<>();
        RosterQuery deptQuery4=new RosterQuery();
        deptQuery4.setEmploymentDate(month);
        deptQuery4.setDeptId(90L);
        deptQuery4.setInviteUerId(SecurityUtils.getUserId());
        sysRosterListZN=sysRosterMapper.selectThisMonthList(deptQuery4);

        //平台入职数
        List<SysRoster> sysRosterListPT=new ArrayList<>();
        RosterQuery deptQuery5=new RosterQuery();
        deptQuery5.setEmploymentDate(month);
        deptQuery5.setDeptId(90L);
        deptQuery5.setInviteUerId(SecurityUtils.getUserId());
        sysRosterListPT=sysRosterMapper.selectThisMonthList(deptQuery5);



        //查询该部门下所有的子部门
        DeptQuery que=new DeptQuery();
        que.setParentIdList(vo.getParentIdList());
        List<SysDept> deptList = sysDeptMapper.selectDeptList(que);
        //循环查询所有部门当前月的入职数量
        RosterQuery deptQueryAll=new RosterQuery();
        deptQueryAll.setEmploymentDate(month);
        for (SysDept sysDept : deptList) {
            deptQueryAll.setDeptId(sysDept.getDeptId());
            List<SysRoster> sysRosterList=sysRosterMapper.selectThisMonthList(deptQueryAll);
            sysDept.setRosterEntryLis(sysRosterList);

            HashMap<String,List<SysRoster>> rosterMap=getInterviewCount(sysRosterList);
            //员工
            List<SysRoster>  YGList =rosterMap.get("YGList");
            //管理
            List<SysRoster>  GLList =rosterMap.get("GLList");
            sysDept.setCommissionerEntryCount(YGList.size());
            sysDept.setManageEntryCount(GLList.size());

            //查询该部门计划入职员工数和管理数量
            LambdaQueryWrapper<SysRecruit> wrapperSysRecruit = new LambdaQueryWrapper<>();
            wrapperSysRecruit.like(SysRecruit::getMonth, month);
            wrapperSysRecruit.like(SysRecruit::getDemandDepartment, sysDept.getDeptName());
            List<SysRecruit> recruitList=this.baseMapper.selectList(wrapperSysRecruit);
            Long commissionerDept=0L;
            Long manageDept=0L;
            for (SysRecruit sysRecruit : recruitList) {
                if (sysRecruit.getCommissioner()!=null) {
                    commissionerDept=commissionerDept+sysRecruit.getCommissioner();
                }
                if (sysRecruit.getManage()!=null) {
                    manageDept=manageDept+sysRecruit.getManage();
                }
                sysDept.setCommissionerEntryCountPlan(Math.toIntExact(commissionerDept));
                sysDept.setManageEntryCountPlan(Math.toIntExact(manageDept));
            }
        }
        vo.setDeptList(deptList);

        //入职角色管理 员工分类
        //vip
        HashMap<String,List<SysRoster>> mapTG=getInterviewCount(sysRosterListTGVIP);
        //推广VIP员工入职数量
        List<SysRoster>  VIPYGList =mapTG.get("YGList");
        //推广VIP管理入职数量
        List<SysRoster>  VIPGLList =mapTG.get("GLList");

        //运营
        HashMap<String,List<SysRoster>> mapYY=getInterviewCount(sysRosterListYY);
        //运营员工入职数量
        List<SysRoster>  YYYGList =mapYY.get("YGList");
        //运营管理入职数量
        List<SysRoster>  YYGLList =mapYY.get("GLList");

        //职能
        HashMap<String,List<SysRoster>> mapZN=getInterviewCount(sysRosterListZN);
        //职能员工入职数量
        List<SysRoster>  ZNYGList =mapZN.get("YGList");
        //职能管理入职数量
        List<SysRoster>  ZNGLList =mapZN.get("GLList");

        //平台
        HashMap<String,List<SysRoster>> mapPT=getInterviewCount(sysRosterListPT);
        //平台员工入职数量
        List<SysRoster>  PTYGList =mapPT.get("YGList");
        //平台管理入职数量
        List<SysRoster>  PTGLList =mapPT.get("GLList");

        vo.setPromotionGLCount(VIPGLList.size());
        vo.setPromotionYGCount(VIPYGList.size());

        vo.setOperationGLCount(YYGLList.size());
        vo.setOperationYGCount(YYYGList.size());

        vo.setFunctionGLCount(ZNGLList.size());
        vo.setFunctionYGCount(ZNYGList.size());

        vo.setPlatformGLCount(PTGLList.size());
        vo.setPlatformYGCount(PTYGList.size());

        vo.setPromotionCountEntry(sysRosterListTGVIP.size());
        vo.setOperationCountEntry(sysRosterListYY.size());
        vo.setFunctionCountEntry(sysRosterListZN.size());
        vo.setPlatformCountEntry(sysRosterListPT.size());


        //统计本月推广，VIP数量计划数
        LambdaQueryWrapper<SysRecruit> wrapperTg = new LambdaQueryWrapper<>();
        wrapperTg.like(SysRecruit::getMonth, month);
        wrapperTg.eq(SysRecruit::getRecruitType, 1);
        List<SysRecruit> tgList = sysRecruitMapper.selectList(wrapperTg);
        vo.setPromotionCount(tgList.size());

        //统计统计本月推广，VIP专员计划数量 管理计划数量
        long TGGLCount=0L;
        long TGZYCount=0L;
        for (SysRecruit sysRecruit : tgList) {
            if (sysRecruit.getCommissioner()!=null){
                TGZYCount=TGZYCount+sysRecruit.getCommissioner();
            }
            if (sysRecruit.getManage()!=null){
                TGGLCount=TGGLCount+sysRecruit.getManage();
            }
        }
        vo.setPromotionGLPlanCount(TGGLCount);
        vo.setPromotionYGPlanCount(TGZYCount);


        //统计本月运营数量
        LambdaQueryWrapper<SysRecruit> wrapperYY = new LambdaQueryWrapper<>();
        wrapperYY.like(SysRecruit::getMonth, month);
        wrapperYY.eq(SysRecruit::getRecruitType, 2);
        List<SysRecruit> yYList = sysRecruitMapper.selectList(wrapperYY);
        vo.setOperationCount(yYList.size());

        //统计统计本月运营，专员计划数量 管理计划数量
        long YYGLCount=0L;
        long YYZYCount=0L;
        for (SysRecruit sysRecruit : yYList) {
            if (sysRecruit.getCommissioner()!=null){
                YYZYCount=YYZYCount+sysRecruit.getCommissioner();
            }
            if (sysRecruit.getManage()!=null){
                YYGLCount=YYGLCount+sysRecruit.getManage();

            }
        }
        vo.setOperationGPlanLCount(YYGLCount);
        vo.setOperationYGPlanCount(YYZYCount);


        //统计本月职能数量
        LambdaQueryWrapper<SysRecruit> wrapperZn = new LambdaQueryWrapper<>();
        wrapperZn.like(SysRecruit::getMonth, month);
        wrapperZn.eq(SysRecruit::getRecruitType, 3);
        List<SysRecruit> zNList = sysRecruitMapper.selectList(wrapperZn);
        vo.setFunctionCount(zNList.size());

        //统计统计本月职能，专员计划数量 管理计划数量
        long ZNGLCount=0L;
        long ZNZYCount=0L;
        for (SysRecruit sysRecruit : zNList) {
            if (sysRecruit.getManage()!=null){
                ZNGLCount=ZNGLCount+sysRecruit.getManage();
            }
            if (sysRecruit.getCommissioner()!=null){
                ZNZYCount=ZNZYCount+sysRecruit.getCommissioner();
            }
        }
        vo.setFunctionGLPlanCount(ZNGLCount);
        vo.setFunctionYGPlanCount(ZNZYCount);

        //统计本月平台数量
        LambdaQueryWrapper<SysRecruit> wrapperPt = new LambdaQueryWrapper<>();
        wrapperPt.like(SysRecruit::getMonth, month);
        wrapperPt.eq(SysRecruit::getRecruitType, 4);
        List<SysRecruit> pTList = sysRecruitMapper.selectList(wrapperPt);
        vo.setPlatformCount(pTList.size());

        //统计统计本月平台，专员计划数量 管理计划数量
        long PTGLCount=0L;
        long PTZYCount=0L;
        for (SysRecruit sysRecruit : pTList) {
            if (sysRecruit.getManage()!=null){
                PTGLCount=PTGLCount+sysRecruit.getManage();
            }
            if (sysRecruit.getCommissioner()!=null){
                PTZYCount=PTZYCount+sysRecruit.getCommissioner();
            }
        }
        vo.setPlatformGLPlanCount(PTGLCount);
        vo.setPlatformYGPlanCount(PTZYCount);

        //本月我的招聘任务
        LambdaQueryWrapper<SysRecruitUserSubmit> wrapperMy = new LambdaQueryWrapper<>();
        wrapperMy.like(SysRecruitUserSubmit::getHireDate, month);
        wrapperMy.eq(SysRecruitUserSubmit::getUserId, SecurityUtils.getUserId());
        List<SysRecruitUserSubmit> mYList = sysRecruitUserSubmitMapper.selectList(wrapperMy);
        Long mySumManage=0L;
        Long mySumCommissioner=0L;
        for (SysRecruitUserSubmit submit : mYList) {
            if (submit.getManage()!=null){
                mySumManage=mySumManage+submit.getManage();
            }
            if (submit.getCommissioner()!=null){
                mySumCommissioner=mySumCommissioner+submit.getCommissioner();
            }
        }
        Long myTotal=mySumManage+mySumCommissioner;
        vo.setMySumManage(mySumManage);
        vo.setMySumCommissioner(mySumCommissioner);
        vo.setMyTotal(myTotal);

        //统计本月我邀约的面试数量
        LambdaQueryWrapper<SysInterview> wrapperMS = new LambdaQueryWrapper<>();
        wrapperMS.like(SysInterview::getInterviewDate, month);
        wrapperMS.eq(SysInterview::getInviteUserId, SecurityUtils.getUserId());
        List<SysInterview> mSList=sysInterviewMapper.selectList(wrapperMS);
        vo.setInterviewCount(mSList.size());

        //统计本月复试数量
        LambdaQueryWrapper<SysInterview> wrapperFS = new LambdaQueryWrapper<>();
        wrapperFS.like(SysInterview::getInterviewDate, month);
        wrapperFS.eq(SysInterview::getInviteUserId, SecurityUtils.getUserId());
        wrapperFS.isNotNull(SysInterview::getRecruitUserId);
        List<SysInterview> fSList=sysInterviewMapper.selectList(wrapperFS);
        vo.setRetrialCount(fSList.size());

        //统计本月入职数量
        RosterQuery  rosterQuery = new RosterQuery();
        rosterQuery.setInviteUerId(SecurityUtils.getUserId());
        rosterQuery.setEmploymentDate(month);
        List<SysRosterVo> rZlist=sysRosterMapper.getRosterByMouthList(rosterQuery);
        vo.setEntryCount(rZlist.size());

        //统计本月待入职数量
        LambdaQueryWrapper<SysInterview> wrapperDRZ = new LambdaQueryWrapper<>();
        wrapperDRZ.like(SysInterview::getEstimateEmploymentDate, month);
        wrapperDRZ.eq(SysInterview::getInviteUserId, SecurityUtils.getUserId());
        List<SysInterview> drZList=sysInterviewMapper.selectList(wrapperDRZ);
        vo.setTreatEntryCount(drZList.size());

        //统计本月待转正数量
        RosterQuery  rosterQuery1 = new RosterQuery();
        rosterQuery.setInviteUerId(SecurityUtils.getUserId());
        rosterQuery.setPlanFormalDate(month);
        List<SysRosterVo> dZZlist=sysRosterMapper.getRosterByMouthList(rosterQuery1);
        vo.setStraightCount(dZZlist.size());
        return vo;
    }

    /**
     * 根据各部门已入职集合分类员工入职数量和管理入职数
     * @return
     */
    public  HashMap<String,List<SysRoster>> getInterviewCount(List<SysRoster> sysRosterList) {
        List<SysRoster> YGList=new ArrayList<>();
        List<SysRoster> GLList=new ArrayList<>();
        for(SysRoster sysRoster : sysRosterList){
            LambdaQueryWrapper<SysInterview> wrapperMS = new LambdaQueryWrapper<>();
            wrapperMS.eq(SysInterview::getInterviewId, sysRoster.getInterviewId());
            SysInterview interview=sysInterviewMapper.selectOne(wrapperMS);
            if(interview!=null){
                if (interview.getInterviewRole()!=null){
                    //除了员工其他的都是管理
                    if (interview.getInterviewRole().equals("5")){
                        YGList.add(sysRoster);
                    }else{
                        GLList.add(sysRoster);
                    }
                }
            }
        }
        HashMap<String,List<SysRoster>> map = new HashMap<>();
        map.put("YGList",YGList);
        map.put("GLList",GLList);
        return map;
    }






    /**
     * 设置操作记录
     * @param recruit
     * @return
     */
    public SysRecruitOperate setProperty(SysRecruitVo recruit) {
        SysRecruitOperate sysRecruitOperate=new SysRecruitOperate();
        sysRecruitOperate.setRecruitId(recruit.getId());
        sysRecruitOperate.setUserId(SecurityUtils.getUserId());
        sysRecruitOperate.setName(SecurityUtils.getUsername());
        sysRecruitOperate.setOperationType(recruit.getOperationType());
        sysRecruitOperate.setDept(recruit.getDemandDepartment());
        sysRecruitOperate.setManage(recruit.getManage());
        sysRecruitOperate.setCommissioner(recruit.getCommissioner());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDateTime = now.format(formatter);
        sysRecruitOperate.setOperateTime(formattedDateTime);
        int hour = now.getHour(); // 获取当前小时
        int minute = now.getMinute();// 获取当前分钟
        String stringHour=String.valueOf(hour);
        String stringMinute=String.valueOf(minute);
        if (hour<10){
            stringHour="0"+stringHour;
        }
        if (minute<10){
            stringMinute="0"+stringMinute;
        }
        sysRecruitOperate.setOperateDate(stringHour + ":" + stringMinute);
        return sysRecruitOperate;
    }
}
