package com.yooa.system.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.UserConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.system.api.domain.SysPost;
import com.yooa.system.api.domain.query.PostQuery;
import com.yooa.system.mapper.SysPostMapper;
import com.yooa.system.mapper.SysUserPostMapper;
import com.yooa.system.service.PostService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 岗位信息 服务层处理
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class PostServiceImpl extends ServiceImpl<SysPostMapper, SysPost> implements PostService {
    private final SysUserPostMapper userPostMapper;

    /**
     * 根据条件分页查询岗位信息集合
     *
     * @param query 岗位信息
     * @return 岗位信息集合
     */
    @Override
    public List<SysPost> selectPostList(Page<SysPost> page, PostQuery query) {
        return baseMapper.selectPostList(page, query);
    }

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    @Override
    public List<Long> selectPostListByUserId(Long userId) {
        return baseMapper.selectPostListByUserId(userId);
    }

    /**
     * 校验岗位名称是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostNameUnique(SysPost post) {
        Long postId = ObjUtil.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = baseMapper.checkPostNameUnique(post.getPostName());
        if (ObjUtil.isNotNull(info) && info.getPostId().longValue() != postId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码是否唯一
     *
     * @param post 岗位信息
     * @return 结果
     */
    @Override
    public boolean checkPostCodeUnique(SysPost post) {
        Long postId = ObjUtil.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = baseMapper.checkPostCodeUnique(post.getPostCode());
        if (ObjUtil.isNotNull(info) && info.getPostId().longValue() != postId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    @Override
    public int countUserPostById(Long postId) {
        return userPostMapper.countUserPostById(postId);
    }

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    @Override
    public int deletePostByIds(List<Long> postIds) {
        for (Long postId : postIds) {
            SysPost post = baseMapper.selectById(postId);
            if (countUserPostById(postId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", post.getPostName()));
            }
        }
        return baseMapper.deleteBatchIds(postIds);
    }
}
