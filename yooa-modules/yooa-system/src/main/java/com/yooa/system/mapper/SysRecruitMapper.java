package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.SysRole;
import com.yooa.system.api.domain.query.RoleQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import com.yooa.system.api.domain.vo.SysRoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招聘计划 数据层
 *
 * <AUTHOR>
 */
public interface SysRecruitMapper extends BaseMapper<SysRecruit> {
    /**
     * 分页查询
     * @return 招聘计划数据集合
     */
    public List<SysRecruitVo> selectRecruitList(Page<SysRecruitVo> page,@Param("query") SysRecruitVo vo);




    /**
     * 分页查询
     * @return 统计部门招聘岗位
     */
    public List<SysRecruitVo> getRecruitDeptCount(Page<SysRecruitVo> page,@Param("query") SysRecruitVo vo);



}
