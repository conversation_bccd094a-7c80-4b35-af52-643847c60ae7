package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.domain.SysYooaAttendGroup;
import com.yooa.system.domain.vo.AttendGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 考勤组
 */
public interface SysYooaAttendGroupMapper extends BaseMapper<SysYooaAttendGroup> {


    List<AttendGroupVo> queryByNameLike(@Param("groupName") String groupName);

    SysYooaAttendGroup queryByNameAndId(@Param("groupName") String groupName, @Param("groupId") Long groupId);

    void deleteGroup(@Param("groupId") Long groupId);
}




