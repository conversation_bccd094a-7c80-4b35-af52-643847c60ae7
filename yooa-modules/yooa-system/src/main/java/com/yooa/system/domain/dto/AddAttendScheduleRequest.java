package com.yooa.system.domain.dto;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.*;


@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddAttendScheduleRequest extends QueryEntity {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 排班id
     */
    private Long classId;
    /**
     * 考勤组
     */
    private Long groupId;
    /**
     * 考勤日期
     */
    private String planDate;


}
