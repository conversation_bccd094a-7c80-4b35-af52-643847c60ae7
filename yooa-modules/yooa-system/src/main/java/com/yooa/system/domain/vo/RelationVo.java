package com.yooa.system.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class RelationVo implements Serializable {

    /**
     * 关联类型 1 部门 2 人员
     */
    private String relationType;

    /**
     * 关联id
     */
    private Long  relationId;

    /**
     * 名称
     */
    private String relationName;

}
