package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.system.api.domain.SysInstruction;
import com.yooa.system.mapper.SysInstructionMapper;
import com.yooa.system.service.InstructionService;
import org.springframework.stereotype.Service;

/**
 * 使用说明 - 服务实现层
 */
@Service
public class InstructionServiceImpl extends ServiceImpl<SysInstructionMapper, SysInstruction> implements InstructionService {

}
