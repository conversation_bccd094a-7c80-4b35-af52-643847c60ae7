package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.RosterSensitiveVo;
import com.yooa.system.api.domain.vo.RosterVo;
import com.yooa.system.api.domain.vo.SysRosterVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 花名册/员工 - 数据层
 */
public interface SysRosterMapper extends BaseMapper<SysRoster> {

    List<RosterSensitiveVo> selectRosterList(Page<RosterSensitiveVo> page, @Param("query") RosterQuery query);

    RosterVo selectRosterById(@Param("rosterId") Long rosterId);

    List<Long> selectRosterByDeptIdList(@Param("deptIdList") List<Long> deptIdList, @Param("realName") String realName);

    List<SysRoster> selectByRealNameAndId(@Param("realName") String realName, @Param("rosterId") Long rosterId);

    /**
     * 获取所有在职人员
     * @return
     */
    List<SysRosterVo> selectAllRoster();

    /**
     * 根据部门查询对应花名册用户
     */
    List<SysRosterVo> selectAllRosterByDept(@Param("deptIdList") List<Long> deptIdList);


    /**
     * 根据id查询
     */
    List<SysRosterVo> selectByRosterIdList(@Param("idList") List<Long> idList);

    /**
     * 不分页获取离职获取转正人数
     * @return
     */
    List<SysRosterVo> selectRoster(@Param("query") RosterQuery query);

    /**
     * 不分页获取当月入职 邀约人为当前用户
     * @return
     */
    List<SysRosterVo> getRosterByMouthList(@Param("query") RosterQuery query);

    /**
     * 查询这个月入职的人的部门
     * @return
     */
    public List<SysRoster> selectThisMonthList(@Param("query") RosterQuery query);
}




