package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.RosterSensitiveVo;
import com.yooa.system.api.domain.vo.RosterVo;
import com.yooa.system.domain.vo.DeptAndRosterVo;

import java.util.List;

/**
 * 花名册 - 服务层
 */
public interface RosterService extends IService<SysRoster> {

    /**
     * 获取花名册列表 - 数据脱敏返回
     */
    List<RosterSensitiveVo> getRosterList(Page<RosterSensitiveVo> page, RosterQuery query);

    List<RosterSensitiveVo> getAllRosterList(Page<RosterSensitiveVo> page, RosterQuery query);

    RosterVo getRosterById(Long rosterId);

    /**
     * 根据钉钉id获取花名册信息
     */
    SysRoster getByDingUserId(String dingUserId);

    /**
     * 根据身份证号码获取花名册信息
     */
    SysRoster getByIdCardNumber(String idCardNumber);

    /**
     * 钉钉入职同步花名册信息
     */
    Integer dingTalkAddRoster(List<String> userIds);

    /**
     * 钉钉离职同步花名册信息
     */
    Integer dingTalkLeaveRoster(List<String> userIds);

    /**
     * 查询所有部门跟员工花名册数据
     */
     DeptAndRosterVo allDeptAndRoster();

}
