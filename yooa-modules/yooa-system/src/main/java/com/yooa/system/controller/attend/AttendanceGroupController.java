package com.yooa.system.controller.attend;

import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.system.domain.dto.AttendGroupRequest;
import com.yooa.system.domain.vo.AttendGroupRelationVo;
import com.yooa.system.domain.vo.AttendGroupViewVo;
import com.yooa.system.domain.vo.AttendGroupVo;
import com.yooa.system.service.AttendGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 参数配置 - 考勤
 */
@RestController
@RequestMapping("/attendance/group")
public class AttendanceGroupController extends BaseController {



    @Autowired
    private AttendGroupService attendGroupService;


    /**
     *查询考勤组列表
     * @param groupName
     * @return
     */
    @GetMapping("/list")
    public R<List<AttendGroupVo>> list(@RequestParam(value = "groupName", required = false) String groupName) {
        return R.ok(attendGroupService.list(groupName));
    }


    @PostMapping("/add")
    public R queryAttendList(@RequestBody AttendGroupRequest request) {
        attendGroupService.add(request);
        return R.ok();
    }

    @GetMapping ("/view")
    public R<AttendGroupViewVo> view(@RequestParam("groupId") Long groupId) {
        return R.ok(attendGroupService.view(groupId));
    }

    @GetMapping ("/queryClassAndRelation")
    public R<AttendGroupRelationVo> queryClassAndRelation(@RequestParam("groupId") Long groupId) {
        return R.ok(attendGroupService.queryClassAndRelation(groupId));
    }

    @GetMapping ("/delete")
    public R delete(@RequestParam("groupId") Long groupId) {
        attendGroupService.delete(groupId);
        return R.ok();
    }

    @PostMapping ("/update")
    public R update(@RequestBody  AttendGroupRequest request) {
        attendGroupService.update(request);
        return R.ok();
    }



}
