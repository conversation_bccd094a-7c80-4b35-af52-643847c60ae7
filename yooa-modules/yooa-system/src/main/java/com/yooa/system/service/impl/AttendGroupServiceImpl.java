package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.vo.SysRosterVo;
import com.yooa.system.domain.SysGroupRelation;
import com.yooa.system.domain.SysYooaAttendClass;
import com.yooa.system.domain.SysYooaAttendGroup;
import com.yooa.system.domain.dto.AttendGroupRequest;
import com.yooa.system.domain.vo.*;
import com.yooa.system.mapper.*;
import com.yooa.system.service.AttendGroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class AttendGroupServiceImpl extends ServiceImpl<SysYooaAttendGroupMapper, SysYooaAttendGroup> implements AttendGroupService {


    @Autowired
    private SysGroupRelationMapper sysGroupRelationMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysRosterMapper sysRosterMapper;
    @Autowired
    private SysYooaAttendClassMapper sysYooaAttendClassMapper;



    @Override
    public List<AttendGroupVo> list(String groupName) {
        return baseMapper.queryByNameLike(groupName);
    }

    @Override
    public void delete(Long groupId) {
        baseMapper.deleteGroup(groupId);
        sysGroupRelationMapper.deleteByGroupId(groupId);
    }

    @Override
    public void add(AttendGroupRequest request) {
        check(request);
        SysYooaAttendGroup attendGroup = baseMapper.queryByNameAndId(request.getGroupName(),null);
        if (attendGroup != null) {
            throw new ServiceException("考勤组名称已存在");
        }
        SysYooaAttendGroup sysYooaAttendGroup = new SysYooaAttendGroup();
        sysYooaAttendGroup.setGroupName(request.getGroupName());
        sysYooaAttendGroup.setClassId(request.getClassIds());
        baseMapper.insert(sysYooaAttendGroup);
        for (RelationVo relationVo : request.getRelationVoList()) {
            SysGroupRelation sysGroupRelation = new SysGroupRelation();
            sysGroupRelation.setRelationId(relationVo.getRelationId());
            sysGroupRelation.setGroupId(sysYooaAttendGroup.getGroupId());
            sysGroupRelation.setRelationType(relationVo.getRelationType());
            sysGroupRelationMapper.insert(sysGroupRelation);
        }
    }

    @Override
    public AttendGroupViewVo view(Long groupId) {
        SysYooaAttendGroup sysYooaAttendGroup = baseMapper.selectById(groupId);
        if (sysYooaAttendGroup == null) {
            throw new ServiceException("查询的考勤组不存在");
        }
        AttendGroupViewVo attendGroupViewVo = new AttendGroupViewVo();
        attendGroupViewVo.setGroupName(sysYooaAttendGroup.getGroupName());
        attendGroupViewVo.setGroupId(sysYooaAttendGroup.getGroupId());
        List<String> list = Arrays.asList(sysYooaAttendGroup.getClassId().split(","));
        List<Long> idList = new ArrayList<>();
        for (String classId : list) {
            idList.add(Long.parseLong(classId));
        }
        attendGroupViewVo.setClassIds(idList);
        List<SysGroupRelation> sysGroupRelationList = sysGroupRelationMapper.queryByGroupId(groupId);
        if (sysGroupRelationList == null || sysGroupRelationList.isEmpty()) {
            return attendGroupViewVo;
        }
        List<RelationVo> relationVoList = new ArrayList<>();
        for (SysGroupRelation sysGroupRelation : sysGroupRelationList) {
            RelationVo relationVo = new RelationVo();
            relationVo.setRelationType(sysGroupRelation.getRelationType());
            relationVo.setRelationId(sysGroupRelation.getRelationId());
            if ("1".equals(sysGroupRelation.getRelationType())) {
                relationVo.setRelationName(sysDeptMapper.selectDeptById(sysGroupRelation.getRelationId()).getDeptName());
            }else {
                relationVo.setRelationName(sysRosterMapper.selectRosterById(sysGroupRelation.getRelationId()).getRealName());
            }
            relationVoList.add(relationVo);
        }
        attendGroupViewVo.setRelationVoList(relationVoList);
        return attendGroupViewVo;
    }

    @Override
    public AttendGroupRelationVo queryClassAndRelation(Long groupId) {
        SysYooaAttendGroup sysYooaAttendGroup = baseMapper.selectById(groupId);
        if (sysYooaAttendGroup == null) {
            throw new ServiceException("考勤组不存在");
        }
        AttendGroupRelationVo attendGroupRelationVo = new AttendGroupRelationVo();
        List<SysYooaAttendClass> sysYooaAttendClassList =  new ArrayList<>();
        List<String> list = Arrays.asList(sysYooaAttendGroup.getClassId().split(","));
        for (String classId : list) {
            SysYooaAttendClass sysYooaAttendClass = sysYooaAttendClassMapper.selectById(Long.parseLong(classId));
            if (sysYooaAttendClass == null) {
                continue;
            }
            sysYooaAttendClassList.add(sysYooaAttendClass);
        }
        attendGroupRelationVo.setSysYooaAttendClassList(sysYooaAttendClassList);
        List<SysGroupRelation> sysGroupRelationList = sysGroupRelationMapper.queryByGroupId(groupId);
        if (sysGroupRelationList == null || sysGroupRelationList.isEmpty()) {
            return attendGroupRelationVo;
        }
        List<String> nameList = new ArrayList<>();
        List<SysRosterVo> sysRosterVoList = new ArrayList<>();
        for (SysGroupRelation sysGroupRelation : sysGroupRelationList) {
            if ("1".equals(sysGroupRelation.getRelationType())) {
                SysDept sysDept = sysDeptMapper.selectDeptById(sysGroupRelation.getRelationId());
                if (sysDept == null) {
                    continue;
                }
                // 查询该部门的子部门
                List<SysDept> sysDeptList = sysDeptMapper.selectAllChildrenDeptById(sysGroupRelation.getRelationId());
                if (sysDeptList == null || sysDeptList.isEmpty()) {
                    sysDeptList = new ArrayList<>();
                    sysDeptList.add(sysDept);
                } else {
                    sysDeptList.add(sysDept);
                }
                // 查询花名册中对应该部门下的员工
                List<Long> depatIdList = sysDeptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
                List<SysRosterVo> rosterVoList = sysRosterMapper.selectAllRosterByDept(depatIdList);
                sysRosterVoList.addAll(rosterVoList);
                nameList.add(sysDept.getDeptName());
            }else {
                SysRoster sysRoster = sysRosterMapper.selectRosterById(sysGroupRelation.getRelationId());
                if (sysRoster == null) {
                    continue;
                }
                SysRosterVo sysRosterVo = new SysRosterVo();
                sysRosterVo.setRealName(sysRoster.getRealName());
                sysRosterVo.setUserId(sysRoster.getRosterId());
                sysRosterVoList.add(sysRosterVo);
                nameList.add(sysRoster.getRealName());
            }
        }
        List<SysRosterVo> distinctList = sysRosterVoList.stream()
                .collect(Collectors.toMap(
                        SysRosterVo::getUserId,  // 使用userId作为key
                        Function.identity(),     // 保留原始对象作为value
                        (existing, replacement) -> existing  // 如果key冲突，保留已存在的对象
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
        attendGroupRelationVo.setSysRosterVoList(distinctList);
        attendGroupRelationVo.setRelationNameList(nameList);
        return attendGroupRelationVo;
    }

    private void check(AttendGroupRequest request) {
        if (StringUtils.isBlank(request.getGroupName())) {
            throw new ServiceException("考勤组名称不能为空");
        }
        if (StringUtils.isBlank(request.getClassIds())) {
            throw new ServiceException("考勤组关联的考勤班次不能为空");
        }
        if (request.getRelationVoList() == null || request.getRelationVoList().size() == 0) {
            throw new ServiceException("考勤组关联的部门或者人员不能为空");
        }

    }

    @Override
    public void update(AttendGroupRequest request) {
        check(request);
        if (request.getGroupId() == null) {
            throw new ServiceException("修改考勤组对应id不能为空");
        }
        SysYooaAttendGroup attendGroup = baseMapper.queryByNameAndId(request.getGroupName(),request.getGroupId());
        if (attendGroup != null) {
            throw new ServiceException("考勤组名称已存在");
        }
        SysYooaAttendGroup sysYooaAttendGroup = new SysYooaAttendGroup();
        sysYooaAttendGroup.setGroupName(request.getGroupName());
        sysYooaAttendGroup.setClassId(request.getClassIds());
        sysYooaAttendGroup.setGroupId(request.getGroupId());
        baseMapper.updateById(sysYooaAttendGroup);
        sysGroupRelationMapper.deleteByGroupId(request.getGroupId());
        for (RelationVo relationVo : request.getRelationVoList()) {
            SysGroupRelation sysGroupRelation = new SysGroupRelation();
            sysGroupRelation.setRelationId(relationVo.getRelationId());
            sysGroupRelation.setGroupId(sysYooaAttendGroup.getGroupId());
            sysGroupRelation.setRelationType(relationVo.getRelationType());
            sysGroupRelationMapper.insert(sysGroupRelation);
        }
    }


}
