package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysReservation;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import com.yooa.system.api.domain.vo.SysReservationVo;

import java.util.List;

/**
 * 预约面试表 数据层
 *
 * <AUTHOR>
 */
public interface SysReservationMapper extends BaseMapper<SysReservation> {
    /**
     * 分页查询
     * @return 预约信息列表
     */
    public List<SysReservationVo> getReservationList(Page<SysReservationVo> page);
}
