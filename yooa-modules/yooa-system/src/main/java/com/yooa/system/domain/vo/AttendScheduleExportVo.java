package com.yooa.system.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;
/**
 * <AUTHOR> xh
 * @Date: 2025/3/7 13:05
 * @Description:
 */
@Data
public class AttendScheduleExportVo {

    /**
     * 主播id
     */
    @Excel(name = "姓名")
    private String realName;

    /**
     * pd账号
     **/
    @Excel(name = "01")
    private String month01;

    /**
     * pd账号
     **/
    @Excel(name = "02")
    private String month02;
    /**
     * pd账号
     **/
    @Excel(name = "03")
    private String month03;
    /**
     * pd账号
     **/
    @Excel(name = "04")
    private String month04;

    /**
     * pd账号
     **/
    @Excel(name = "05")
    private String month05;
    /**
     * pd账号
     **/
    @Excel(name = "06")
    private String month06;



    /**
     * pd账号
     **/
    @Excel(name = "07")
    private String month07;

    /**
     * pd账号
     **/
    @Excel(name = "08")
    private String month08;
    /**
     * pd账号
     **/
    @Excel(name = "09")
    private String month09;
    /**
     * pd账号
     **/
    @Excel(name = "10")
    private String month10;

    /**
     * pd账号
     **/
    @Excel(name = "11")
    private String month11;
    /**
     * pd账号
     **/
    @Excel(name = "12")
    private String month12;

    /**
     * pd账号
     **/
    @Excel(name = "13")
    private String month13;

    /**
     * pd账号
     **/
    @Excel(name = "14")
    private String month14;
    /**
     * pd账号
     **/
    @Excel(name = "15")
    private String month15;

    /**
     * pd账号
     **/
    @Excel(name = "16")
    private String month16;

    /**
     * pd账号
     **/
    @Excel(name = "17")
    private String month17;
    /**
     * pd账号
     **/
    @Excel(name = "18")
    private String month18;

    /**
     * pd账号
     **/
    @Excel(name = "19")
    private String month19;
    /**
     * pd账号
     **/
    @Excel(name = "20")
    private String month20;
    /**
     * pd账号
     **/
    @Excel(name = "21")
    private String month21;
    /**
     * pd账号
     **/
    @Excel(name = "22")
    private String month22;
    /**
     * pd账号
     **/
    @Excel(name = "23")
    private String month23;
    /**
     * pd账号
     **/
    @Excel(name = "24")
    private String month24;

    /**
     * pd账号
     **/
    @Excel(name = "25")
    private String month25;
    /**
     * pd账号
     **/
    @Excel(name = "26")
    private String month26;
    /**
     * pd账号
     **/
    @Excel(name = "27")
    private String month27;
    /**
     * pd账号
     **/
    @Excel(name = "28")
    private String month28;
    /**
     * pd账号
     **/
    @Excel(name = "29")
    private String month29;

    /**
     * pd账号
     **/
    @Excel(name = "30")
    private String month30;
    /**
     * pd账号
     **/
    @Excel(name = "31")
    private String month31;



}
