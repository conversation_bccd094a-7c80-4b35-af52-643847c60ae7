package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysConfig;
import com.yooa.system.api.domain.query.ConfigQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 参数配置 - 数据层
 */
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 查询参数配置列表
     */
    public List<SysConfig> selectConfigList(Page<SysConfig> page, @Param("query") ConfigQuery query);
}