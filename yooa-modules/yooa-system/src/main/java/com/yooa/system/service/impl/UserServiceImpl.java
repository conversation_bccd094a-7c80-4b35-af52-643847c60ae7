package com.yooa.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.RemoteCmfAdminService;
import com.yooa.cmf.api.RemoteCmfUserService;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.UserConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.bean.BeanValidators;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.*;
import com.yooa.system.api.domain.dto.UserEditDto;
import com.yooa.system.api.domain.dto.UserSaveDto;
import com.yooa.system.api.domain.query.PublicUserQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.PublicUserVo;
import com.yooa.system.api.domain.vo.SysUserNoticeVo;
import com.yooa.system.api.domain.vo.SysUserVo;
import com.yooa.system.mapper.*;
import com.yooa.system.service.ConfigService;
import com.yooa.system.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class UserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements UserService {
    protected final Validator validator;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final SysUserNoticeMapper userNoticeMapper;
    private final ConfigService configService;
    private final SysUserPdMapper userPdMapper;
    private final RemoteCmfUserService remoteCmfUserService;
    private final RemoteCmfAdminService remoteCmfAdminService;

    /**
     * 根据条件分页查询用户列表
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserVo> selectUserList(Page<SysUserVo> page, UserQuery query) {
        return baseMapper.selectUserList(page, query);
    }

    /**
     * 根据条件分页查询用户列表 (不走权限)
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserListSpecial(Page<SysUserVo> page, UserQuery query) {
        return baseMapper.selectUserList(page, query);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserVo> selectAllocatedList(Page<SysUserVo> page, UserQuery query) {
        return baseMapper.selectAllocatedList(page, query);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserVo> selectUnallocatedList(Page<SysUserVo> page, UserQuery query) {
        return baseMapper.selectUnallocatedList(page, query);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param nickName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String nickName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(nickName);
        if (CollectionUtils.isEmpty(list)) {
            return StrUtil.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param nickName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String nickName) {
        List<SysPost> list = postMapper.selectPostsByNickName(nickName);
        if (CollectionUtils.isEmpty(list)) {
            return StrUtil.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = ObjUtil.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = baseMapper.checkUserNameUnique(user.getUserName());
        if (ObjUtil.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    /**
     * 校验用户是否允许操作
     *
     * @param userId id
     */
    @Override
    public void checkUserAllowed(Long userId) {
        if (ObjUtil.isNotNull(userId) && SecurityUtils.isAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            if (ObjUtil.isEmpty(baseMapper.selectById(userId))) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(UserSaveDto user) {
        // 新增用户信息
        int rows = baseMapper.insert(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色关联
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return baseMapper.insert(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(UserEditDto user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        // 修改用户与pd绑定
        if (ArrayUtil.isNotEmpty(user.getPdUserId())) {
            userPdMapper.delete(Wrappers.<SysUserPd>lambdaQuery().eq(SysUserPd::getUserId, userId));
            Arrays.stream(user.getPdUserId()).forEach(pdUserId -> {
                userPdMapper.insert(SysUserPd.builder().pdUserId(pdUserId).userId(userId).build());
            });
        }
        return baseMapper.updateById(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }


    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(UserSaveDto user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(UserSaveDto user) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return baseMapper.deleteById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(List<Long> userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return baseMapper.deleteBatchIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport) {
        if (CollUtil.isEmpty(userList)) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.getConfigValueByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = baseMapper.selectUserByUserName(user.getUserName());
                if (ObjUtil.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    baseMapper.insert(user);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 导入成功");
                }
                else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u.getUserId());
                    checkUserDataScope(u.getUserId());
                    user.setUserId(u.getUserId());
                    baseMapper.updateById(user);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 更新成功");
                }
                else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(user.getUserName()).append(" 已存在");
                }
            }
            catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<SysUserNoticeVo> getMyNoticeList(String status,String noticeType) {
        return userNoticeMapper.selectUserNoticeListByUserId(SecurityUtils.getUserId(), status,noticeType);
    }

    @Override
    public Long getMyUnreadNoticeNumber() {
        return userNoticeMapper.selectCount(
                Wrappers.<SysUserNotice>lambdaQuery()
                        .eq(SysUserNotice::getStatus, DictConstants.SYS_READ_NO)
                        .eq(SysUserNotice::getUserId, SecurityUtils.getUserId())
        );
    }

    @Override
    public List<PublicUserVo> getPublicUserList(PublicUserQuery query) {
        return baseMapper.selectPublicUserList(query);
    }

    @Override
    public List<PublicUserVo> getPublicPageUserList(Page<PublicUserVo> page, PublicUserQuery query) {
        return baseMapper.selectPublicPageUserList(page,query);
    }
}
