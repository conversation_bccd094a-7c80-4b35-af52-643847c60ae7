package com.yooa.system.domain.dto;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.*;


@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendScheduleRequest extends QueryEntity {
    /**
     * 考勤组id
     */
    private Long groupId;
    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 员工姓名
     */
    private String realName;

}
