package com.yooa.system.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class AttendCheckVo implements Serializable {

    /**
     * 日期
     */
    private String date;
    /**
     * 是否排班 0工作日  1休息日
     */
    private String wetherSchedule;
    /**
     * 异常数据信息
     */
    private List<AttendUserCheckVo> attendIllegalVoList;
    /**
     * 打卡数据
     */
    private List<AttendUserCheckVo> attendUserCheckVoList;
    /**
     * 班次名称
     */
    private String className;
    /**
     * 计划上班时间
     */
    private String onTime;
    /**
     * 计划下班时间
     */
    private String offTime;



}
