package com.yooa.system.controller.attend;

import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.system.domain.dto.AddAttendScheduleRequest;
import com.yooa.system.domain.dto.AttendScheduleRequest;
import com.yooa.system.domain.vo.AttendYooaScheduleVo;
import com.yooa.system.service.AttendScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 参数配置 - 考勤
 */
@RestController
@RequestMapping("/attendance/schedule")
public class AttendanceScheduleController extends BaseController {



    @Autowired
    private AttendScheduleService attendScheduleService;


    /**
     * 查询用户排班
     * @param request
     * @return
     */
    @GetMapping("/list")
    public R<AttendYooaScheduleVo> queryAttendScheduleList(AttendScheduleRequest request) {
        return R.ok(attendScheduleService.queryList(request));
    }

    /**
     * 查询用户排班
     * @param request
     * @return
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, AttendScheduleRequest request) {
        attendScheduleService.export(response, request);
    }

    /**
     * 保存用户排班信息
     * @param requestList
     * @return
     */
    @PostMapping("/add")
    public R add(@RequestBody List<AddAttendScheduleRequest> requestList) {
        attendScheduleService.add(requestList);
        return R.ok();
    }

}
