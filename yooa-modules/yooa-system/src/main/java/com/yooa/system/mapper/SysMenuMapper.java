package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.api.domain.SysMenu;
import com.yooa.system.api.domain.query.MenuQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单 - 数据层
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {
    /**
     * 查询系统菜单列表
     */
    public List<SysMenu> selectMenuList(@Param("query") MenuQuery query);

    /**
     * 根据用户所有权限
     */
    public List<String> selectMenuPerms();

    /**
     * 根据用户查询系统菜单列表
     */
    public List<SysMenu> selectMenuListByUserId(@Param("query") MenuQuery query);

    /**
     * 根据角色ID查询权限
     */
    public List<String> selectMenuPermsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限
     */
    public List<String> selectMenuPermsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询菜单
     */
    public List<SysMenu> selectMenuTreeAll();

    /**
     * 根据用户ID查询菜单
     */
    public List<SysMenu> selectMenuTreeByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询菜单树信息
     */
    public List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId, @Param("menuCheckStrictly") boolean menuCheckStrictly);

    /**
     * 是否存在菜单子节点
     */
    public int hasChildByMenuId(@Param("menuId") Long menuId);

    /**
     * 校验菜单名称是否唯一
     */
    public SysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);
}
