package com.yooa.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.data.style.Style;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.system.api.RemoteFileService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.SysFile;
import com.yooa.system.api.domain.SysInterview;
import com.yooa.system.api.domain.dto.CommunicationStyleSaveDto;
import com.yooa.system.api.domain.query.InterviewQuery;
import com.yooa.system.api.domain.vo.InterviewListVo;
import com.yooa.system.api.domain.vo.InterviewVo;
import com.yooa.system.mapper.SysDictDataMapper;
import com.yooa.system.mapper.SysInterviewMapper;
import com.yooa.system.service.InterviewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 面试信息 - 业务实现层
 */
@Slf4j
@AllArgsConstructor
@Service
public class InterviewServiceImpl extends ServiceImpl<SysInterviewMapper, SysInterview>
        implements InterviewService {

    private final RemoteFileService remoteFileService;
    private final SysDictDataMapper dictDataMapper;

    @Override
    public List<InterviewListVo> getInterviewList(Page<InterviewListVo> page, InterviewQuery query) {
        return baseMapper.selectInterviewList(page, query);
    }

    @Override
    public InterviewVo getInterviewById(Long interviewId) {
        return baseMapper.selectInterviewById(interviewId);
    }

    @Override
    public List<SysInterview> listByIdCardNumber(String idCardNumber) {
        return baseMapper.selectList(
                Wrappers.<SysInterview>lambdaQuery()
                        .eq(SysInterview::getIdCardNumber, idCardNumber)
        );
    }

    @Override
    public String getRegistrationFormUrl(Long interviewId) {
        InterviewVo interview = baseMapper.selectInterviewById(interviewId);
        if (ObjUtil.isNull(interview)) {
            throw new ServiceException("面试信息不存在");
        }

        if (StrUtil.isNotBlank(interview.getRegistrationFormUrl())) {
            return interview.getRegistrationFormUrl();
        }

        HashMap<String, Object> data = new HashMap<>();
        data.put("InterviewDate", interview.getInterviewDate());
        data.put("InviteUserName", interview.getInviteNickName());
        data.put("Position", interview.getPosition());
        data.put("SalaryExpectation", interview.getSalaryExpectation());
        data.put("Lodging", getDictLabel("common_default_status", interview.getIsLodging()));
        data.put("InterviewName", interview.getInterviewName());
        data.put("Age", interview.getAge());
        data.put("Sex", getDictLabel("common_sex_type", interview.getSex()));
        data.put("NativePlace", interview.getNativePlace());
        data.put("Nation", interview.getNation());
        data.put("Marital", getDictLabel("common_marital_status", interview.getMaritalStatus()));
        data.put("BirthdayDate", interview.getBirthdayDate());
        data.put("ChineseBirthdayDate", interview.getChineseBirthdayDate());
        data.put("IdCardNumber", interview.getIdCardNumber());
        data.put("Education", getDictLabel("common_education_type", interview.getEducationType()));
        data.put("Major", interview.getMajor());
        data.put("HealthStatus", getDictLabel("common_health_status", interview.getHealthStatus()));
        data.put("IsInfectiousDiseases", getDictLabel("common_default_status", interview.getIsInfectiousDiseases()));
        data.put("Phone", interview.getPhone());
        data.put("Email", interview.getEmail());
        data.put("EmergencyContactName", interview.getEmergencyContactName());
        data.put("EmergencyContactPhone", interview.getEmergencyContactPhone());
        data.put("RegisteredResidence", interview.getRegisteredResidence());
        data.put("Address", interview.getAddress());
        data.put("CareerPlanning", interview.getCareerPlanning());
        data.put("IsNca", getDictLabel("common_default_status", interview.getIsNca()));
        data.put("RelativesName", interview.getRelativesName());
        data.put("RelativesRelationship", getDictLabel("common_relationship_type", interview.getRelativesRelationship()));

        // 学历扩展信息
        JSONArray educationExtra = JSON.parseArray(interview.getEducationExtra());
        if (ObjUtil.isNotNull(educationExtra)) {
            for (int i = 0; i < educationExtra.size(); i++) {
                data.put("EnrollmentDate" + (i + 1), educationExtra.getJSONObject(i).getString("enrollmentDate"));
                data.put("GraduationDate" + (i + 1), educationExtra.getJSONObject(i).getString("graduationDate"));
                data.put("School" + (i + 1), educationExtra.getJSONObject(i).getString("school"));
                data.put("Major" + (i + 1), educationExtra.getJSONObject(i).getString("major"));
                data.put("Education" + (i + 1), educationExtra.getJSONObject(i).getString("education"));
                data.put("Degree" + (i + 1), educationExtra.getJSONObject(i).getString("degree"));
            }
        }

        // 工作扩展信息
        JSONArray workExtra = JSON.parseArray(interview.getWorkExtra());
        if (ObjUtil.isNotNull(workExtra)) {
            for (int i = 0; i < workExtra.size(); i++) {
                data.put("EmploymentDate" + (i + 1), workExtra.getJSONObject(i).getString("employmentDate"));
                data.put("ResignationDate" + (i + 1), workExtra.getJSONObject(i).getString("resignationDate"));
                data.put("Company" + (i + 1), workExtra.getJSONObject(i).getString("company"));
                data.put("Position" + (i + 1), workExtra.getJSONObject(i).getString("position"));
                data.put("Salary" + (i + 1), workExtra.getJSONObject(i).getString("salary"));
                data.put("Certifier" + (i + 1), workExtra.getJSONObject(i).getString("certifier"));
                data.put("CertifierPhone" + (i + 1), workExtra.getJSONObject(i).getString("certifierPhone"));
            }
        }

        // 家庭扩展信息
        JSONArray familyExtra = JSON.parseArray(interview.getFamilyExtra());
        if (ObjUtil.isNotNull(familyExtra)) {
            for (int i = 0; i < familyExtra.size(); i++) {
                data.put("MemberName" + (i + 1), familyExtra.getJSONObject(i).getString("memberName"));
                data.put("Relationship" + (i + 1), familyExtra.getJSONObject(i).getString("relationship"));
                data.put("ContactPhone" + (i + 1), familyExtra.getJSONObject(i).getString("contactPhone"));
                data.put("Work" + (i + 1), familyExtra.getJSONObject(i).getString("work"));
                data.put("remark" + (i + 1), familyExtra.getJSONObject(i).getString("remark"));
            }
        }

        // 最看重
        String mostConcerned = interview.getMostConcerned();
        data.put("mc1", new TextRenderData(StrUtil.count(mostConcerned, "1") > 0 ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("mc2", new TextRenderData(StrUtil.count(mostConcerned, "2") > 0 ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("mc3", new TextRenderData(StrUtil.count(mostConcerned, "3") > 0 ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("mc4", new TextRenderData(StrUtil.count(mostConcerned, "4") > 0 ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("mc5", new TextRenderData(StrUtil.count(mostConcerned, "5") > 0 ? "þ" : "o", new Style("Wingdings", 10)));

        // 是否有犯罪记录
        String isCriminalRecord = interview.getIsCriminalRecord();
        data.put("CriminalRecord_Y", new TextRenderData(isCriminalRecord.equals("Y") ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("CriminalRecord_N", new TextRenderData(isCriminalRecord.equals("N") ? "þ" : "o", new Style("Wingdings", 10)));

        // 是否同意背景调查
        String isBackgroundCheck = interview.getIsBackgroundCheck();
        data.put("BackgroundCheck_Y", new TextRenderData(isBackgroundCheck.equals("Y") ? "þ" : "o", new Style("Wingdings", 10)));
        data.put("BackgroundCheck_N", new TextRenderData(isBackgroundCheck.equals("N") ? "þ" : "o", new Style("Wingdings", 10)));

        // 入职时间
        data.put("EstimateEmploymentDate", interview.getEstimateEmploymentDate());

        try {
            // 获取模板文件
            ClassPathResource resource = new ClassPathResource("file/registration_form.docx");
            // 执行动态渲染数据
            XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream());
            template.render(data);

            // 创建输出流
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            // 渲染后的数据生成至输出流中
            template.writeAndClose(bos);

            // 创建MultipartFile 并将数据放置其中
            FileItemFactory factory = new DiskFileItemFactory();
            FileItem item = factory.createItem(interview.getInterviewName(), "text/plain", false, interview.getInterviewName() + "面试登记表.docx");
            item.getOutputStream().write(bos.toByteArray());

            // 执行上传
            R<SysFile> uploadR = remoteFileService.upload(new CommonsMultipartFile(item));

            if (uploadR.getCode() == R.SUCCESS) {
                String registrationFormUrl = uploadR.getData().getUrl();
                interview.setRegistrationFormUrl(registrationFormUrl);
                baseMapper.updateById(interview);
                return registrationFormUrl;
            }

        }
        catch (Exception e) {
            log.error("获取面试登记表失败", e);
        }

        return null;
    }

    @Override
    public boolean communicationStyle(Long interviewId, CommunicationStyleSaveDto saveDto) {
        SysInterview interview = getById(interviewId);

        List<List<String>> answerList = saveDto.getAnswerList();

        int questionNum = 1;
        Map<Integer, List<String>> answerMap = new HashMap<>();
        for (List<String> answer : answerList) {
            // 1、6、11、16为一组，2、7、12、17为一组，3、8、13、18为一组，4、9、14、19为一组，5、10、15、20为一组
            if (questionNum == 1 || questionNum == 6 || questionNum == 11 || questionNum == 16) {
                List<String> test = CollUtil.newArrayList(answerMap.get(0));
                CollUtil.addAll(test, answer);
                answerMap.put(0, test);
            }
            else if (questionNum == 2 || questionNum == 7 || questionNum == 12 || questionNum == 17) {
                List<String> test = CollUtil.newArrayList(answerMap.get(1));
                CollUtil.addAll(test, answer);
                answerMap.put(1, test);
            }
            else if (questionNum == 3 || questionNum == 8 || questionNum == 13 || questionNum == 18) {
                List<String> test = CollUtil.newArrayList(answerMap.get(2));
                CollUtil.addAll(test, answer);
                answerMap.put(2, test);
            }
            else if (questionNum == 4 || questionNum == 9 || questionNum == 14 || questionNum == 19) {
                List<String> test = CollUtil.newArrayList(answerMap.get(3));
                CollUtil.addAll(test, answer);
                answerMap.put(3, test);
            }
            else if (questionNum == 5 || questionNum == 10 || questionNum == 15 || questionNum == 20) {
                List<String> test = CollUtil.newArrayList(answerMap.get(4));
                CollUtil.addAll(test, answer);
                answerMap.put(4, test);
            }
            questionNum++;
        }

        CommunicationStyleSaveDto.Result result = new CommunicationStyleSaveDto.Result();
        CommunicationStyleSaveDto.VerticalResult verticalResult = new CommunicationStyleSaveDto.VerticalResult();
        List<CommunicationStyleSaveDto.HorizontalResult> horizontalResultList = new ArrayList<>();
        for (List<String> answer : answerMap.values()) {
            String answerStr = CollUtil.join(answer, ",");

            CommunicationStyleSaveDto.HorizontalResult horizontalResult = new CommunicationStyleSaveDto.HorizontalResult();

            horizontalResult.setDm(StrUtil.count(answerStr, "Dm"));
            horizontalResult.setIm(StrUtil.count(answerStr, "Im"));
            horizontalResult.setSm(StrUtil.count(answerStr, "Sm"));
            horizontalResult.setCm(StrUtil.count(answerStr, "Cm"));
            horizontalResult.setDl(StrUtil.count(answerStr, "Dl"));
            horizontalResult.setIl(StrUtil.count(answerStr, "Il"));
            horizontalResult.setSl(StrUtil.count(answerStr, "Sl"));
            horizontalResult.setCl(StrUtil.count(answerStr, "Cl"));
            horizontalResultList.add(horizontalResult);

            verticalResult.setDm(verticalResult.getDm() + horizontalResult.getDm());
            verticalResult.setIm(verticalResult.getIm() + horizontalResult.getIm());
            verticalResult.setSm(verticalResult.getSm() + horizontalResult.getSm());
            verticalResult.setCm(verticalResult.getCm() + horizontalResult.getCm());
            verticalResult.setDl(verticalResult.getDl() + horizontalResult.getDl());
            verticalResult.setIl(verticalResult.getIl() + horizontalResult.getIl());
            verticalResult.setSl(verticalResult.getSl() + horizontalResult.getSl());
            verticalResult.setCl(verticalResult.getCl() + horizontalResult.getCl());
        }

        result.setHorizontalResult(horizontalResultList);
        result.setVerticalResult(verticalResult);
        result.setD(verticalResult.getDm() - verticalResult.getDl());
        result.setI(verticalResult.getIm() - verticalResult.getIl());
        result.setS(verticalResult.getSm() - verticalResult.getSl());
        result.setC(verticalResult.getCm() - verticalResult.getCl());

        saveDto.setResult(result);
        interview.setCommunicationStyleExtra(JSON.toJSONString(saveDto));
        return updateById(interview);
    }


    private String getDictLabel(String dictType, String dictValue) {
        List<SysDictData> DictDataList = dictDataMapper.selectDictDataByType(dictType);
        for (SysDictData dictData : DictDataList) {
            if (dictData.getDictValue().equals(dictValue)) {
                return dictData.getDictLabel();
            }
        }
        return null;
    }
}




