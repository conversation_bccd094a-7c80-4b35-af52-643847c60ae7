package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysAttendRecord;
import com.yooa.system.domain.dto.UserAttendCheckQuery;
import com.yooa.system.domain.vo.AttendCheckVo;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
public interface SyncAttendUserCheckService extends IService<SysAttendRecord> {

    /**
     * 同步打卡数据
     */
    public void syncAttendUserCheck();


    /**
     * 查询用户考勤数据
     */
    public List<AttendCheckVo> queryAttendList (UserAttendCheckQuery query);

}
