package com.yooa.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.utils.SpringUtils;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.SysNotice;
import com.yooa.system.api.domain.SysRecruitCount;
import com.yooa.system.api.domain.SysUserNotice;
import com.yooa.system.api.domain.dto.NoticeEditDto;
import com.yooa.system.api.domain.dto.NoticeSaveDto;
import com.yooa.system.api.domain.query.NoticeQuery;
import com.yooa.system.api.domain.vo.SysNoticeVo;
import com.yooa.system.api.domain.vo.SysUserNoticeVo;
import com.yooa.system.api.domain.vo.SysUserVo;
import com.yooa.system.mapper.SysNoticeMapper;
import com.yooa.system.mapper.SysUserMapper;
import com.yooa.system.mapper.SysUserNoticeMapper;
import com.yooa.system.service.NoticeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 通知 - 服务层实现
 */
@AllArgsConstructor
@Service
public class NoticeServiceImpl extends ServiceImpl<SysNoticeMapper, SysNotice> implements NoticeService {

    private final SysUserMapper userMapper;
    private final SysUserNoticeMapper userNoticeMapper;

    /**
     * 系统通知列表
     */
    @Override
    public List<SysNoticeVo> getNoticeList(Page<SysNoticeVo> page, NoticeQuery query) {
        return baseMapper.selectNoticeList(page, query);
    }

    /**
     * 系统通知详情
     */
    @Override
    public SysNoticeVo getNoticeById(Long noticeId) {
        SysNoticeVo sysNoticeVo = baseMapper.selectNoticeById(noticeId);
        List<SysUserNoticeVo> sysUserNoticeVos = userNoticeMapper.selectUserNoticeListByNoticeId(noticeId);
        sysNoticeVo.setUnreadUserList(sysUserNoticeVos.stream().filter(un -> un.getStatus().equals(DictConstants.SYS_READ_NO)).toList());
        sysNoticeVo.setReadUserList(sysUserNoticeVos.stream().filter(un -> un.getStatus().equals(DictConstants.SYS_READ_YES)).toList());
        return sysNoticeVo;
    }

    /**
     * 保存系统通知
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveNotice(NoticeSaveDto noticeSaveDto) {
        SysNotice sysNotice = new SysNotice();
        BeanUtil.copyProperties(noticeSaveDto, sysNotice);

        // step1: 数据初始化
        sysNotice.setDeptIds(StrUtil.join(StrUtil.COMMA, noticeSaveDto.getDeptIds()));
        sysNotice.setStatus(DictConstants.SYS_NOTICE_STATUS_UNPUBLISHED);

        // step2: 执行新增通知
        int row = baseMapper.insert(sysNotice);

        // step3: 若通知新增成功并且保存类型是提交则执行添加其关联用户
        if (row > 0 && noticeSaveDto.getSendType().equals(DictConstants.SYS_NOTICE_SEND_NOW) && noticeSaveDto.getSaveType().equals("2")) {
            SpringUtils.getAopProxy(this).sendNotice(sysNotice.getNoticeId());
        }
        return row;
    }

    /**
     * 修改系统通知
     */
    @Override
    public int editNotice(NoticeEditDto noticeEditDto) {
        SysNotice sysNotice = new SysNotice();
        BeanUtil.copyProperties(noticeEditDto, sysNotice);

        // step1: 数据初始化
        sysNotice.setDeptIds(StrUtil.join(StrUtil.COMMA, noticeEditDto.getDeptIds()));
        sysNotice.setStatus(DictConstants.SYS_NOTICE_STATUS_UNPUBLISHED);

        // step2: 执行新增通知
        int row = baseMapper.updateById(sysNotice);

        // step3: 若通知新增成功并且发送类型是立即发送则执行添加其关联用户
        if (row > 0 && noticeEditDto.getSaveType().equals("2")) {
            SpringUtils.getAopProxy(this).sendNotice(sysNotice.getNoticeId());
        }
        return row;
    }

    /**
     * 删除系统通知
     */
    @Override
    public int removeNoticeByIds(List<Long> noticeIds) {
        return baseMapper.batchUpdateNoticeStatus(noticeIds, DictConstants.SYS_NOTICE_STATUS_DELETE);
    }

    /**
     * 发送系统通知
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int sendNotice(Long noticeId) {
        SysNotice sysNotice = baseMapper.selectById(noticeId);

        List<Long> deptIds = Arrays.stream(sysNotice.getDeptIds().split(StrUtil.COMMA)).map(Long::valueOf).toList();
        // step1: 查询范围(部门)内所有的用户集
        List<SysUserVo> sysUserVos = userMapper.selectAllChildrenUserByDeptIds(deptIds);

        // step2: 遍历用户集重新生成为通知用户关联集
        List<SysUserNotice> userNotices = sysUserVos.stream().map(user -> {
            SysUserNotice userNotice = new SysUserNotice();
            userNotice.setUserId(user.getUserId());
            userNotice.setNoticeId(sysNotice.getNoticeId());
            userNotice.setStatus(DictConstants.SYS_READ_NO);
            return userNotice;
        }).toList();

        // step3: 新增通知用户关联集
        userNoticeMapper.batchInsert(userNotices);

        // step4: 修改通知发送状态以及发送时间
        sysNotice.setStatus(DictConstants.SYS_NOTICE_STATUS_UNREAD);
        sysNotice.setSendTime(LocalDateTime.now());
        return baseMapper.updateById(sysNotice);
    }

    /**
     * 查收系统通知
     */
    @Override
    public int readNotice(Long noticeId) {
        SysUserNotice userNotice = userNoticeMapper.selectOne(Wrappers.<SysUserNotice>lambdaQuery().eq(SysUserNotice::getNoticeId, noticeId).eq(SysUserNotice::getUserId, SecurityUtils.getUserId()));
        if (ObjUtil.isEmpty(userNotice)) {
            // throw new ServiceException("该用户不在通知范围内，无法查收");
            return 0;
        }
        userNotice.setStatus(DictConstants.SYS_READ_YES);
        return userNoticeMapper.update(userNotice, Wrappers.<SysUserNotice>lambdaUpdate().eq(SysUserNotice::getNoticeId, noticeId).eq(SysUserNotice::getUserId, SecurityUtils.getUserId()));
    }

    /**
     * 定时发送系统通知
     */
    @Override
    public int sendNoticeByTiming() {
        LocalDateTime now = LocalDateTime.now();
        List<SysNotice> sysNotices = baseMapper.selectList(
                Wrappers.<SysNotice>lambdaQuery()
                        .eq(SysNotice::getSendType, DictConstants.SYS_NOTICE_SEND_TIMING)
                        .eq(SysNotice::getStatus, DictConstants.SYS_NOTICE_STATUS_UNPUBLISHED)
                        .between(SysNotice::getSendTime, now.withMinute(0), now.withMinute(59))
        );
        sysNotices.forEach(sn -> {
            SpringUtils.getAopProxy(this).sendNotice(sn.getNoticeId());
        });
        return sysNotices.size();
    }

    /**
     * 同步系统通知状态为已读
     */
    @Override
    public int syncNoticeStatusIsRead() {
        List<SysNotice> notices = baseMapper.selectList(
                Wrappers.<SysNotice>lambdaQuery()
                        .eq(SysNotice::getStatus, DictConstants.SYS_NOTICE_STATUS_UNREAD)
        );
        List<Long> noticeIds = notices.stream().map(SysNotice::getNoticeId).toList();

        List<Long> readNoticeIds = userNoticeMapper.selectListIsReadByNoticeIds(noticeIds);

        if (CollUtil.isNotEmpty(readNoticeIds)) {
            baseMapper.batchUpdateNoticeStatus(readNoticeIds, DictConstants.SYS_NOTICE_STATUS_READ);
        }

        return readNoticeIds.size();
    }

    @Override
    public SysNoticeVo syncNoticeCount() {
        SysNoticeVo vo = new SysNoticeVo();
        Long userId=SecurityUtils.getUserId();
        List<SysUserNoticeVo> listPlan=userNoticeMapper.selectUserNoticeListByUserId(userId,null,"7");
        vo.setPlanCount(listPlan.size());
        List<SysUserNoticeVo> listOther=userNoticeMapper.selectUserNoticeListByUserId(userId,null,"6");
        vo.setOtherCount(listOther.size());
        return vo;
    }
}
