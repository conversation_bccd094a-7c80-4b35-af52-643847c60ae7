package com.yooa.system.listen;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.yooa.system.service.RosterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DingTalkEventListener {

    private final RosterService rosterService;

    @KafkaListener(topics = "_dingtalk_event", groupId = "system-group")
    @RetryableTopic(
            attempts = "3",
            backoff = @Backoff(delay = 2000L, multiplier = 2),
            dltTopicSuffix = "-dlt",
            exclude = DataIntegrityViolationException.class
    )
    public void listener(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) throws UnirestException, IOException {
        // 解析消息并提取所需参数
        String message = consumerRecord.value();
        if (StrUtil.isNotBlank(message)) {
            JSONObject jsonObject = JSONObject.parseObject(message);
            String eventType = jsonObject.getString("eventType");
            List<String> dingUserIds = JSONObject.parse(jsonObject.getString("data")).getList("userId", String.class);
            // 员工入职
            if (eventType.equals("user_add_org")) {
                rosterService.dingTalkAddRoster(dingUserIds);
            }
            // 员工离职
            if (eventType.equals("user_leave_org")) {
                rosterService.dingTalkLeaveRoster(dingUserIds);
            }
        }

        // 提交ACK
        acknowledgment.acknowledge();
    }


}
