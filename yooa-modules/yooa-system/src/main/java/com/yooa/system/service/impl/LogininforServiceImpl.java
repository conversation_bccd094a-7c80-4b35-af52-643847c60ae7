package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.system.api.domain.SysLogininfor;
import com.yooa.system.api.domain.query.LogininforQuery;
import com.yooa.system.mapper.SysLogininforMapper;
import com.yooa.system.service.LogininforService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class LogininforServiceImpl extends ServiceImpl<SysLogininforMapper, SysLogininfor> implements LogininforService {

    /**
     * 查询分页系统登录日志集合
     *
     * @param query 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(Page<SysLogininfor> page, LogininforQuery query) {
        return baseMapper.selectLogininforList(page, query);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor() {
        baseMapper.cleanLogininfor();
    }
}
