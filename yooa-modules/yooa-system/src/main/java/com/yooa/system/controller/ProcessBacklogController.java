package com.yooa.system.controller;

import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.RemoteGoalExtendService;
import com.yooa.system.api.RemoteApprovalService;
import com.yooa.system.api.domain.SysProcessBacklog;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.vo.SysProcessBacklogVo;
import com.yooa.system.api.domain.vo.TodayCountVo;
import com.yooa.system.service.ProcessBacklogService;
import io.lettuce.core.dynamic.annotation.Param;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务流程控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/processBacklog")
public class ProcessBacklogController extends BaseController {

    private final ProcessBacklogService processBacklogService;


    /**
     * 查询我的代办发起
     */
    @PostMapping("/getProcessBacklogList")
    public AjaxResult getProcessBacklogList(@Validated @RequestBody SysProcessBacklogVo vo) {
        SysProcessBacklogVo sysProcessBacklogVo = processBacklogService.selectProcessBacklogList(vo);
        List<SysProcessBacklogVo> processBacklogList=sysProcessBacklogVo.getProcessBacklogListPage();
        int page=vo.getPage();
        int pageSize=vo.getPageSize();
        List<SysProcessBacklogVo> processBacklogListPage = processBacklogService.getPageProcessBack(processBacklogList, page, pageSize);
        sysProcessBacklogVo.setProcessBacklogListPage(processBacklogListPage);
        return success(sysProcessBacklogVo);
    }

    /**
     * 根据流程ID查询关联的人员统计数详情
     */
    @PostMapping("/getDetails")
    public AjaxResult getDetails(@Validated @RequestBody SysProcessBacklogVo vo) {
        SysProcessBacklogVo vo1=processBacklogService.getDetails(vo);
        return success(vo1);
    }

    /**
     *根据流程ID查询招聘计划
     */
    @GetMapping("/getSysRecruitList")
    public AjaxResult getSysRecruitList(@Param("id") Long id) {
        return success(processBacklogService.getSysRecruitList(id));
    }


    /**
     * 发布/保存聘计划
     * @return
     */
    @PutMapping("/releasePlan")
    public AjaxResult releasePlan(@Validated @RequestBody SysProcessBacklogVo vo) {
        int state=processBacklogService.releasePlan(vo);
        if (state==1){
            return AjaxResult.error("专员分配数量不能大于可分配数!");
        }
        if (state==2){
            return AjaxResult.error("主管分配数量不能大于可分配数!");
        }
        if (state==0){
            return AjaxResult.error("请至少给一个人事专员分配专员或者管理数量！");
        }else{
            return success("操作成功");
        }
    }

    /**
     * 驳回后重新提交
     * @return
     */
    @PutMapping("/initiate")
    public AjaxResult initiate(@Validated @RequestBody SysProcessBacklogVo vo) {
        return success(processBacklogService.initiate(vo));
    }

    /**
     * 今日工作概括
     * @return
     */
    @GetMapping("/geTodayCountVo")
    public AjaxResult geTodayCountVo() {
        return AjaxResult.success(processBacklogService.getTodayCountVo());
    }

    /**
     * 月计划年计划招聘数量统计
     * @return
     */
    @PutMapping("/getRecruitCount")
    public AjaxResult getRecruitCount(@Validated @RequestBody SysProcessBacklogVo vo) {
        return AjaxResult.success(processBacklogService.getPlanCount(vo));
    }

    /**
     *根据流程id删除
     */
    @DeleteMapping ("/delete")
    public AjaxResult delete(@Param("id") Long id) {
        int state=processBacklogService.deleteBacklog(id);
       if (state==2){
           return AjaxResult.error("只允许删除驳回和追回状态的流程！");
       }else{
           return success("删除成功！");
       }
    }
}
