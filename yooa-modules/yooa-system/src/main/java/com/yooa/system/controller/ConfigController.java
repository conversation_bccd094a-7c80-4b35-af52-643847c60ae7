package com.yooa.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.system.api.domain.SysConfig;
import com.yooa.system.api.domain.dto.ConfigEditDto;
import com.yooa.system.api.domain.dto.ConfigSaveDto;
import com.yooa.system.api.domain.query.ConfigQuery;
import com.yooa.system.service.ConfigService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置 - 控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/config")
public class ConfigController extends BaseController {
    private final ConfigService configService;

    /**
     * 参数配置列表
     */
    @RequiresPermissions("system:config:list")
    @GetMapping("/list")
    public AjaxResult list(Page<SysConfig> page, ConfigQuery query) {
        return success(page.setRecords(configService.getConfigList(page, query)));
    }

    /**
     * 参数配置详情
     */
    @RequiresPermissions("system:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId) {
        return success(configService.getById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @RequiresPermissions("system:config:query")
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey) {
        return success(configService.getConfigValueByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @RequiresPermissions("system:config:add")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult save(@Validated @RequestBody ConfigSaveDto config) {
        if (!configService.checkConfigKeyUnique(config.getConfigKey())) {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        return toAjax(configService.saveConfig(config));
    }

    /**
     * 修改参数配置
     */
    @RequiresPermissions("system:config:edit")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ConfigEditDto config) {
        if (!configService.checkConfigKeyUnique(config.getConfigKey())) {
            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        return toAjax(configService.editConfig(config));
    }

    /**
     * 删除参数配置
     */
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable List<Long> configIds) {
        configService.removeConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @RequiresPermissions("system:config:remove")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache() {
        configService.resetConfigCache();
        return success();
    }
}
