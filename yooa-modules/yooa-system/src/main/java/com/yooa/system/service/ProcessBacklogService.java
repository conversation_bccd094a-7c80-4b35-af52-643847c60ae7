package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.extend.api.domain.vo.PlanCountVo;
import com.yooa.system.api.domain.SysProcessBacklog;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.vo.SysProcessBacklogVo;
import com.yooa.system.api.domain.vo.TodayCountVo;

import java.util.List;

/**
 * 招聘计划业务层
 *
 * <AUTHOR>
 */
public interface ProcessBacklogService extends IService<SysProcessBacklog> {
    /**
     * @return 业务流程我的代办发起集合信息（自定义流程一起返回）
     */
    public SysProcessBacklogVo selectProcessBacklogList(SysProcessBacklogVo vo);


    /**
     * 手动分页
     * @param sourceList
     * @param page
     * @param pageSize
     * @return
     */
    public List<SysProcessBacklogVo> getPageProcessBack(List<SysProcessBacklogVo> sourceList, int page, int pageSize);


    /**
     * @return 根据流程ID查询当前流程下计划的招聘人员和专员管理统计数
     */
    public SysProcessBacklogVo getDetails(SysProcessBacklogVo vo);


    /**
     * @return 根据流程ID查询当前流程下的招聘计划
     */
    public List<SysRecruit> getSysRecruitList(Long id);

    /**
     * @return 根据流程ID删除流程
     */
    int deleteBacklog(Long id);

    /**
     * @return 发布招聘计划
     */
    int releasePlan(SysProcessBacklogVo vo);


    /**
     * @return 驳回后重新发起流程
     */
    int initiate(SysProcessBacklogVo vo);


    /**
     * 今日工作概括
     * @return
     */
    public TodayCountVo getTodayCountVo();

    /**
     * 今日工作概括
     * @return
     */
    public PlanCountVo getPlanCount(SysProcessBacklogVo vo);

}
