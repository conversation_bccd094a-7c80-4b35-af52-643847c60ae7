package com.yooa.system.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
public class AttendScheduleVo implements Serializable {

    /**
     * 日期
     */
    private Date planDate;
    /**
     * 班次名称
     */
    private String className;
    /**
     * 计划上班时间
     */
    private String onTime;
    /**
     * 计划下班时间
     */
    private String offTime;
    /**
     * 班次id
     */
    private String classId;



}
