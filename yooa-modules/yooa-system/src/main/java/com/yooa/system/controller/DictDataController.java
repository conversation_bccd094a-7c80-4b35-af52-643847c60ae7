package com.yooa.system.controller;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.dto.DictDataEditDto;
import com.yooa.system.api.domain.dto.DictDataSaveDto;
import com.yooa.system.api.domain.query.DictDataQuery;
import com.yooa.system.service.DictDataService;
import com.yooa.system.service.DictTypeService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息 - 控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/dict/data")
public class DictDataController extends BaseController {

    private final DictDataService dictDataService;
    private final DictTypeService dictTypeService;

    @RequiresPermissions("system:dict:list")
    @GetMapping("/list")
    public AjaxResult list(Page<SysDictData> page, DictDataQuery query) {
        return success(page.setRecords(dictDataService.selectDictDataList(page, query)));
    }

    /**
     * 不走权限
     */
    @InnerAuth
    @PostMapping("/getList")
    public R<List<SysDictData>> getList(@RequestBody DictDataQuery query) {
        return R.ok(dictDataService.selectDictDataList(null, query));
    }

    /**
     * 查询字典数据详细
     */
    @RequiresPermissions("system:dict:query")
    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode) {
        return success(dictDataService.getById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (ObjUtil.isNull(data)) {
            data = new ArrayList<SysDictData>();
        }
        return success(data);
    }

    /**
     * 新增字典类型
     */
    @RequiresPermissions("system:dict:add")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody DictDataSaveDto dictDataSaveDto) {
        return toAjax(dictDataService.insertDictData(dictDataSaveDto));
    }

    /**
     * 修改保存字典类型
     */
    @RequiresPermissions("system:dict:edit")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody DictDataEditDto dictDataEditDto) {
        return toAjax(dictDataService.updateDictData(dictDataEditDto));
    }

    /**
     * 删除字典类型
     */
    @RequiresPermissions("system:dict:remove")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable List<Long> dictCodes) {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }
}
