package com.yooa.system.service;

import com.yooa.system.api.domain.vo.SysUserVo;

import java.util.Set;

/**
 * 权限信息 服务层
 *
 * <AUTHOR>
 */
public interface PermissionService {
    /**
     * 获取角色数据权限
     *
     * @param userId 用户Id
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(Long userId);

    /**
     * 获取菜单数据权限
     *
     * @param user 用户
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUserVo user);
}
