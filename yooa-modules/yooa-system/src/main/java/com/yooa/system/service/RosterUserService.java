package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysRosterUser;

import java.util.List;

/**
 * 花名册与用户关联 - 服务层
 *
 * <AUTHOR>
 */
public interface RosterUserService extends IService<SysRosterUser> {

    /**
     * 根据花名册id获取关联用户id列表
     */
    List<SysRosterUser> listByRosterId(Long rosterId);

    /**
     * 根据用户id获取关联花名册id
     */
    SysRosterUser getByUserId(Long userId);

}
