package com.yooa.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.RemoteCmfAdminService;
import com.yooa.cmf.api.domain.CmfAgentAdmin;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.system.api.domain.SysRoster;
import com.yooa.system.api.domain.SysUserPd;
import com.yooa.system.api.domain.vo.UserPdVo;
import com.yooa.system.mapper.SysRosterMapper;
import com.yooa.system.mapper.SysUserMapper;
import com.yooa.system.mapper.SysUserPdMapper;
import com.yooa.system.service.UserPdService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class UserPdServiceImpl extends ServiceImpl<SysUserPdMapper, SysUserPd> implements UserPdService {

    private final SysUserMapper userMapper;
    private final SysRosterMapper rosterMapper;
    private RemoteCmfAdminService remoteCmfAdminService;

    @Override
    public Map<String, List<UserPdVo>> mapByUserId(Long userId) {
        if (ObjUtil.isEmpty(userMapper.selectById(userId))) {
            return MapUtil.empty();
        }

        List<SysUserPd> userPdList = baseMapper.selectList(
                Wrappers.<SysUserPd>lambdaQuery()
                        .eq(SysUserPd::getUserId, userId)
        );

        if (CollUtil.isEmpty(userPdList)) {
            return MapUtil.empty();
        }

        List<UserPdVo> userPdVos = BeanUtil.copyToList(userPdList, UserPdVo.class);

        List<Long> pdUserIds = userPdList.stream().map(SysUserPd::getPdUserId).toList();
        List<CmfAgentAdmin> cmfAgentAdminList = remoteCmfAdminService.listByIds(pdUserIds, SecurityConstants.INNER).getData();
        userPdVos.forEach(userPdVo -> {
            cmfAgentAdminList.forEach(cmfAgentAdmin -> {
                if (userPdVo.getPdUserId().equals(cmfAgentAdmin.getId())) {
                    userPdVo.setPdNickName(cmfAgentAdmin.getNickname());
                }
            });
        });


        return userPdVos.stream().collect(Collectors.groupingBy(SysUserPd::getType));
    }

    @Override
    public Map<String, List<UserPdVo>> mapByRosterId(Long rosterId) {
        SysRoster roster = rosterMapper.selectById(rosterId);
        if (ObjUtil.isEmpty(roster) || ObjUtil.isEmpty(roster.getOaUserId())) {
            return MapUtil.empty();
        }

        return mapByUserId(roster.getOaUserId());
    }
}
