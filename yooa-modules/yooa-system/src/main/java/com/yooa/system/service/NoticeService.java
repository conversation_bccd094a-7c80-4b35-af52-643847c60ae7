package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysNotice;
import com.yooa.system.api.domain.dto.NoticeEditDto;
import com.yooa.system.api.domain.dto.NoticeSaveDto;
import com.yooa.system.api.domain.query.NoticeQuery;
import com.yooa.system.api.domain.vo.SysNoticeVo;

import java.util.List;

/**
 * 通知 - 服务层
 */
public interface NoticeService extends IService<SysNotice> {

    /**
     * 通知列表
     */
    public List<SysNoticeVo> getNoticeList(Page<SysNoticeVo> page, NoticeQuery query);

    /**
     * 通知详情
     */
    public SysNoticeVo getNoticeById(Long noticeId);

    /**
     * 保存通知
     */
    public int saveNotice(NoticeSaveDto sysNotice);

    /**
     * 修改通知
     */
    public int editNotice(NoticeEditDto sysNotice);

    /**
     * 删除通知
     */
    public int removeNoticeByIds(List<Long> noticeIds);

    /**
     * 发送通知
     */
    public int sendNotice(Long noticeId);

    /**
     * 查收通知
     */
    public int readNotice(Long noticeId);

    /**
     * 定时发送通知
     */
    public int sendNoticeByTiming();

    /**
     * 同步通知状态为已读
     */
    public int syncNoticeStatusIsRead();

    /**
     * 通知类型-数量统计
     */
    public SysNoticeVo syncNoticeCount();
}
