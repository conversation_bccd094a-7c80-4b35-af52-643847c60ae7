<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysUserNoticeMapper">

    <select id="selectUserNoticeListByNoticeId" resultType="com.yooa.system.api.domain.vo.SysUserNoticeVo">
        SELECT sun.notice_id, sun.user_id, sun.status, sun.read_time, su.user_name, su.nick_name
        FROM sys_user_notice sun
                 LEFT JOIN sys_user su ON sun.user_id = su.user_id
        WHERE sun.notice_id = #{noticeId}
    </select>

    <select id="selectUserNoticeListByUserId" resultType="com.yooa.system.api.domain.vo.SysUserNoticeVo">
        SELECT sun.notice_id, sun.user_id, sun.status, sun.read_time, su.user_name, su.nick_name, sn.notice_title,
        sn.notice_content, sn.send_time,su.nick_name as createName
        FROM sys_user_notice sun
        LEFT JOIN sys_user su ON sun.user_id = su.user_id
        LEFT JOIN sys_notice sn ON sun.notice_id = sn.notice_id
        WHERE sun.user_id = #{userId}
        <if test="status != null">AND sun.status = #{status}</if>
        <if test="noticeType != null">AND sn.notice_type = #{noticeType}</if>
        ORDER BY sun.`status` ASC,sn.send_time DESC
    </select>

    <select id="selectListIsReadByNoticeIds" resultType="Long">
        SELECT notice_id FROM sys_user_notice
        <where>
            notice_id IN
            <foreach collection="noticeIds" item="noticeId" open="(" close=")" separator=",">
                #{noticeId}
            </foreach>
        </where>
        GROUP BY notice_id HAVING sum( CASE WHEN STATUS = '1' THEN 1 ELSE 0 END ) = sum(1)
    </select>

    <insert id="batchInsert">
        INSERT INTO sys_user_notice (`user_id`, notice_id, status, read_time) VALUES
        <foreach collection="userNoticeList" item="userNotice" separator=",">
            ( #{userNotice.userId}, #{userNotice.noticeId}, #{userNotice.status}, #{userNotice.readTime} )
        </foreach>
    </insert>
</mapper>
