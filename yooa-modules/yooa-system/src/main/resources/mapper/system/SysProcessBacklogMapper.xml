<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysProcessBacklogMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysProcessBacklogVo" id="SysBacklogMap">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="represent" column="represent"/>
        <result property="applicantId" column="applicant_id"/>
        <result property="applicant" column="applicant"/>
        <result property="approverId" column="approver_id"/>
        <result property="approver" column="approver"/>
        <result property="typeNameBig" column="type_name_big"/>
        <result property="typeName" column="type_name"/>
        <result property="state" column="state"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectProcessBacklogList" resultMap="SysBacklogMap">
       select * from sys_process_backlog
        <where>
            <if test="query.getGenre==1">
                and approver_id=#{query.approverId}
            </if>
            <if test="query.getGenre==2">
                and applicant_id=#{query.applicantId}
            </if>
            <if test="query.typeName != null and query.typeName != ''">
                AND type_name like concat('%', #{query.typeName}, '%')
            </if>
            <if test="query.typeNameBig != null and query.typeNameBig != '' and query.typeNameBig != '全部'">
                AND type_name_big like concat('%', #{query.typeNameBig}, '%')
            </if>
            <if test="query.state != null and query.state != ''">
                <if test="query.state==1">
                    AND state in(1,2,5)
                </if>
                <if test="query.state!=1">
                    AND state = #{query.state}
                </if>
            </if>
            <if test="query.title != null and query.title != ''">
                AND title like concat('%', #{query.title}, '%')
            </if>
            <if test="query.planTime != null">
                AND (create_time BETWEEN #{query.planTime[0]} AND #{query.planTime[1]})
            </if>
        </where>
    </select>

</mapper>
