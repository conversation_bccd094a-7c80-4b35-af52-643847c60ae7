<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysAttendScheduleMapper">

    <delete id="deleteByTime">
        DELETE
        FROM sys_attend_schedule
        WHERE plan_date between #{startDate} and #{endDate}
    </delete>


    <select id="queryScheduleByUserAndDate" resultType="com.yooa.system.domain.vo.AttendScheduleVo">
        select  class.class_name,schedule.plan_date,schedule.class_id,class.on_time,class.off_time from sys_attend_schedule schedule
        left join sys_attend_class class on schedule.class_id = class.class_id
        left join sys_roster roster on roster.ding_user_id = schedule.user_id
        left join sys_user user on user.user_id = roster.oa_user_id
        where  user.user_id = #{userId}
        and schedule.plan_date between #{startDate} and #{endDate}
        group by class.class_id,plan_date,class_name
        order by plan_date asc
    </select>
</mapper>