<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysPersonnelChangeRecordMapper">

    <select id="selectPersonnelChangeRecordList" resultType="com.yooa.system.api.domain.vo.PersonnelChangeRecordVo">
        SELECT pcr.*,
               u.nick_name       AS operationNickName,
               d.dept_id         AS operationUserDeptId,
               d.dept_name       AS operationUserDeptName,
               d.ancestors_names AS operationUserDeptAncestorsNames
        FROM sys_personnel_change_record pcr
                 LEFT JOIN sys_user u ON pcr.operation_user_id = u.user_id
                 LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        <where>
            <if test="query.interviewId != null">
                AND pcr.interview_id = #{query.interviewId}
            </if>
            <if test="query.interviewIds != null">
                AND pcr.interview_id IN
                <foreach collection="query.interviewIds" item="interviewId" open="(" close=")" separator=",">
                    #{interviewId}
                </foreach>
            </if>
            <if test="query.changeDate != null">
                AND (DATE(pcr.change_time) >= #{query.changeDate[0]} AND DATE(pcr.change_time) &lt;= #{query.changeDate[1]})
            </if>
        </where>
        ORDER BY pcr.change_time
    </select>
</mapper>
