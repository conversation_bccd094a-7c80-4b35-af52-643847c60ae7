<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysConfigMapper">


    <select id="selectConfigList" resultType="com.yooa.system.api.domain.SysConfig">
        SELECT config_id,
               config_name,
               config_key,
               config_value,
               config_type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        FROM sys_config
        <where>
            <if test="query.configName != null and query.configName != ''">
                AND config_name like concat('%', #{query.configName}, '%')
            </if>
            <if test="query.configType != null and query.configType != ''">
                AND config_type = #{query.configType}
            </if>
            <if test="query.configKey != null and query.configKey != ''">
                AND config_key like concat('%', #{query.configKey}, '%')
            </if>
        </where>
    </select>
</mapper>