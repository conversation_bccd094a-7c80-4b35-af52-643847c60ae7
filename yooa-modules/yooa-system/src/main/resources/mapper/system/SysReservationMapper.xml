<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysReservationMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysReservationVo" id="SysReservationMap">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="time" column="time"/>
        <result property="post" column="post"/>
        <result property="role" column="role"/>
        <result property="salaryExpectation" column="salary_expectation"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="SysReservation">
        SELECT  r.id,
                        r.name,
                        r.sex,
                        r.time,
                        r.post,
                        r.role,
                        r.salary_expectation,
                        r.create_time,
                        r.update_by,
                        r.update_time,
                        r.create_by,
                        r.remark
        FROM sys_reservation r order by  r.create_time desc
    </sql>

    <select id="getReservationList" resultMap="SysReservationMap">
        <include refid="SysReservation"/>
    </select>

</mapper>
