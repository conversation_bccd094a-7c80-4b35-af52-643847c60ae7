<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysRecruitMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysRecruitVo" id="SysRecruitMap">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="processId" column="process_id"/>
        <result property="commissioner" column="commissioner"/>
        <result property="manage" column="manage"/>
        <result property="demandDepartment" column="demand_department"/>
        <result property="state" column="state"/>
        <result property="month" column="month"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="sumCommissioner" column="sumCommissioner"/>
        <result property="sumManage" column="sumManage"/>
        <result property="deptName" column="deptName"/>
    </resultMap>

    <sql id="SysRecruit">
        SELECT  r.id,
                        r.user_id,
                        r.process_id,
                        r.name,
                        r.commissioner,
                        r.manage,
                        r.demand_department,
                        r.state,
                        r.month,
                        r.create_time,
                        r.update_by,
                        r.update_time,
                        r.create_by,
                        r.remark
        FROM sys_recruit r order by  r.create_time desc
    </sql>

    <select id="selectRecruitList" resultMap="SysRecruitMap">
        <include refid="SysRecruit"/>
        <where>
            <if test="query.state != null and query.state != 0">
                and state=#{query.state}
            </if>
        </where>
    </select>

    <select id="selectUndistributed" resultMap="SysRecruitMap">
        SELECT SUM(commissioner) as unCommissioner,SUM(manage) as unManage FROM sys_recruit where state='0';
    </select>


    <select id="getRecruitDeptCount">
        SELECT
            id as id,
            demand_department AS deptName,
            SUM( commissioner ) AS sumCommissioner,
            SUM( manage ) AS sumManage,
            update_time AS updateTime
        FROM
            sys_recruit
        WHERE
            state = #{query.state}
        GROUP BY
            demand_department
    </select>

    <select id="selectRolesByUserName" parameterType="String" >

    </select>

</mapper>
