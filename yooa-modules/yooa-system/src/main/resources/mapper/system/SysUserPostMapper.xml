<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysUserPostMapper">

    <resultMap type="com.yooa.system.api.domain.SysUserPost" id="SysUserPostResult">
        <result property="userId" column="user_id"/>
        <result property="postId" column="post_id"/>
    </resultMap>

    <delete id="deleteUserPostByUserId" parameterType="Long">
        DELETE
        FROM sys_user_post
        WHERE user_id = #{userId}
    </delete>

    <select id="countUserPostById" resultType="Integer">
        SELECT COUNT(1)
        FROM sys_user_post
        WHERE post_id = #{postId}
    </select>

    <delete id="deleteUserPost" parameterType="Long">
        DELETE FROM sys_user_post WHERE user_id IN
        <foreach collection="list" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <insert id="batchUserPost">
        INSERT INTO sys_user_post(user_id, post_id) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.postId})
        </foreach>
    </insert>

</mapper> 