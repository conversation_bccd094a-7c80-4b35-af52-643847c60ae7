<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysRecruitOperateMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysRecruitOperateVo" id="sysRecruitOperateMap">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="recruitId" column="recruit_id"/>
        <result property="operationType" column="operation_type"/>
        <result property="dept" column="dept"/>
        <result property="commissioner" column="commissioner"/>
        <result property="manage" column="manage"/>
        <result property="operateTime" column="operate_time"/>
        <result property="operateDate" column="operate_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="SysSysRecruitOperate">
        select * from sys_recruit_operate r
    </sql>

    <select id="selectRecruitList" resultMap="sysRecruitOperateMap">
        <include refid="SysSysRecruitOperate"/>
        <where>
            <if test="query.recruitIdList.size()>0">
                and r.recruit_id in
                <foreach collection="query.recruitIdList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="query.userId != null and query.userId != 0">
                and r.user_id=#{query.userId}
            </if>
            <if test="query.createDate != null">
                AND (r.create_time BETWEEN #{query.createDate[0]} AND #{query.createDate[1]})
            </if>
        </where>
        order by  r.create_time desc
    </select>

</mapper>
