package com.yooa.extend.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.vo.IndexUserGroupAllVo;
import com.yooa.extend.api.factory.RemoteVermicelliFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 粉丝登记服务
 */
@FeignClient(contextId = "remoteVermicelliService", value = ServiceNameConstants.EXTEND_SERVICE, fallbackFactory = RemoteVermicelliFallbackFactory.class)
public interface RemoteVermicelliService {

    String ClassPath = "vermicelli";

    /********************************************************推广粉丝达成**********************************************************/

    /**
     * 生成推广粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/manufactureVermicelli")
    R<Integer> manufactureVermicelli(@RequestParam("pdExtendIds") List<Long> pdExtendIds,
                                     @RequestParam("extendId") Long extendId,
                                     @RequestParam("extendDeptId") Long extendDeptId,
                                     @RequestParam(value = "serveId", required = false) Long serveId,
                                     @RequestParam(value = "serveDeptId", required = false) Long serveDeptId,
                                     @RequestParam("ids") List<Long> ids,
                                     @RequestParam("friendId") Long friendId,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 生成推广粉丝登记列表(副本:为了防止相互调用)
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/manufactureVermicelliCopy")
    R<Integer> manufactureVermicelliCopy(@RequestParam("pdExtendIds") List<Long> pdExtendIds,
                                         @RequestParam("extendId") Long extendId,
                                         @RequestParam("extendDeptId") Long extendDeptId,
                                         @RequestParam(value = "serveId", required = false) Long serveId,
                                         @RequestParam(value = "serveDeptId", required = false) Long serveDeptId,
                                         @RequestParam("ids") List<Long> ids,
                                         @RequestParam("friendId") Long friendId,
                                         @RequestParam(value = "oneDayUp", required = false) BigDecimal oneDayUp,
                                         @RequestParam(value = "monthUp", required = false) BigDecimal monthUp,
                                         @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询推广粉丝登记汇总数
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selExtendVermicelliNumber")
    R<IndexUserGroupAllVo> selExtendVermicelliNumber(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                     @RequestParam(value = "beginTime", required = false) String beginTime,
                                                     @RequestParam(value = "endTime", required = false) String endTime,
                                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询推广粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selExtendVermicelliList")
    R<List<ExtendVermicelli>> selExtendVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                      @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                      @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                      @RequestParam(value = "beginTime", required = false) String beginTime,
                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询推广粉丝登记列表(特殊查询提供给订单表业务处理使用)
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selOrderExtendVermicelliList")
    R<List<ExtendVermicelli>> selOrderExtendVermicelliList(@RequestParam("userId") Long userId,
                                                           @RequestParam("customerId") Long customerId,
                                                           @RequestParam("orderTime") String orderTime,
                                                           @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增推广粉丝登记
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/addExtendVermicelli")
    R<Boolean> addExtendVermicelli(@RequestBody ExtendVermicelli vermicelli, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量修改推广粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/updExtendVermicelliList")
    R<Boolean> updExtendVermicelliList(@RequestBody List<ExtendVermicelli> vermicelliList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量删除推广粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/removeExtendVermicelliByIds")
    R<Boolean> removeExtendVermicelliByIds(@RequestBody List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /********************************************************VIP粉丝达成**********************************************************/

    /**
     * 查询VIP粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selVipVermicelliList")
    R<List<VipVermicelli>> selVipVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                @RequestParam(value = "beginTime", required = false) String beginTime,
                                                @RequestParam(value = "endTime", required = false) String endTime,
                                                @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询VIP粉丝登记列表(特殊查询提供给订单表业务处理使用)
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selOrderVipVermicelliList")
    R<List<VipVermicelli>> selOrderVipVermicelliList(@RequestParam("userId") Long userId,
                                                     @RequestParam("customerId") Long customerId,
                                                     @RequestParam("orderTime") String orderTime,
                                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增VIP粉丝登记
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/addVipVermicelli")
    R<Boolean> addVipVermicelli(@RequestBody VipVermicelli vermicelli, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量修改VIP粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/updVipVermicelliList")
    R<Boolean> updVipVermicelliList(@RequestBody List<VipVermicelli> vermicelliList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量删除VIP粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/removeVipVermicelliByIds")
    R<Boolean> removeVipVermicelliByIds(@RequestBody List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /********************************************************运营粉丝达成**********************************************************/

    /**
     * 查询运营粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping(ClassPath + "/selOperateVermicelliList")
    R<List<OperateVermicelli>> selOperateVermicelliList(@RequestParam(value = "userIds", required = false) List<Long> userIds,
                                                        @RequestParam(value = "fieldType", required = false) Integer fieldType,
                                                        @RequestParam(value = "customerId", required = false) Long customerId,
                                                        @RequestParam(value = "friendIds", required = false) List<Long> friendIds,
                                                        @RequestParam(value = "beginTime", required = false) String beginTime,
                                                        @RequestParam(value = "endTime", required = false) String endTime,
                                                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增运营粉丝登记
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/addOperateVermicelli")
    R<Boolean> addOperateVermicelli(@RequestBody OperateVermicelli vermicelli, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 批量修改运营粉丝登记列表
     *
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping(ClassPath + "/updOperateVermicelliList")
    R<Boolean> updOperateVermicelliList(@RequestBody List<OperateVermicelli> vermicelliList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
