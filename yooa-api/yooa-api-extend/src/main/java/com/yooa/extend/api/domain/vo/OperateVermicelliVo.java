package com.yooa.extend.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.extend.api.domain.OperateVermicelli;
import lombok.Data;

import java.io.Serializable;

/**
 * 粉丝达成vo
 */
@Data
public class OperateVermicelliVo extends OperateVermicelli implements Serializable {

    /**
     * 客户昵称
     */
    @Excel(name = "用户昵称", sort = 2)
    private String customerNickName;

    @Excel(name = "主播名", sort = 13)
    private String anchorName;

    /**
     * 运营用户名
     */
    private String operateUserName;

    /**
     * 运营昵称
     */
    @Excel(name = "运营员", sort = 6)
    private String operateNickName;

    /**
     * 运营部门名
     */
    @Excel(name = "运营部门", sort = 8)
    private String operateDeptName;

    /**
     * 运营上级部门名
     */
    @Excel(name = "运营上级部门", sort = 7)
    private String operateAncestorsName;

    /**
     * 推广昵称
     */
    @Excel(name = "推广员", sort = 9)
    private String extendNickName;

    /**
     * 推广部门名
     */
    @Excel(name = "推广部门", sort = 10)
    private String extendDeptName;

    /**
     * VIP昵称
     */
    @Excel(name = "客服员", sort = 11)
    private String vipNickName;

    /**
     * VIP部门名
     */
    @Excel(name = "客服部门", sort = 12)
    private String vipDeptName;

    // 交接状态(0:已交接、1:未交接)
    @Excel(name = "交接状态", sort = 14, readConverterExp = "0=交接粉,1=跑骚粉")
    private Integer joinStatus;

}