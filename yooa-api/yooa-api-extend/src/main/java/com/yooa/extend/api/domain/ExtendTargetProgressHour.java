package com.yooa.extend.api.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推广每小时目标完成统计表(不能累加)
 *
 * @TableName extend_target_progress_hour
 */
@Data
public class ExtendTargetProgressHour implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long id;
    /**
     * 日期(含小时)
     */
    private LocalDateTime date;
    /**
     * 推广id
     */
    private Integer agentAdminId;
    /**
     * 优质(推广需要,客服不需要)
     */
    private Integer qualityUsers;
    /**
     * 接收数
     */
    private Integer receive;
    /**
     * 接收数vip/接粉数
     */
    private Integer receiveVip;
    /**
     * 业绩/新增
     */
    private BigDecimal fortyFiveDays;
    /**
     * 汇总业绩
     */
    private BigDecimal realProfit;
    /**
     * 充值用户数量
     */
    private Integer chargeUser;
}