package com.yooa.extend.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.extend.api.domain.ExtendVermicelli;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 粉丝达成vo
 */
@Data
public class ExtendVermicelliVo extends ExtendVermicelli implements Serializable {

    /**
     * 客户昵称
     */
    @Excel(name = "用户昵称", sort = 2)
    private String customerNickName;

    /**
     * 投手人姓名
     */
    @Excel(name = "投手人", sort = 13)
    private String pitcherName;

    /**
     * 推广用户名
     */
    private String extendUserName;

    /**
     * 推广昵称
     */
    @Excel(name = "推广员", sort = 6)
    private String extendNickName;

    /**
     * 推广部门名
     */
    @Excel(name = "推广部门", sort = 8)
    private String extendDeptName;

    /**
     * 推广上级部门名
     */
    @Excel(name = "推广上级部门", sort = 7)
    private String extendAncestorsName;

    /**
     * 客服用户名
     */
    private String vipUserName;

    /**
     * 客服昵称
     */
    @Excel(name = "VIP客服", sort = 9)
    private String vipNickName;

    /**
     * 客服部门名
     */
    @Excel(name = "VIP客服部门", sort = 11)
    private String vipDeptName;

    /**
     * 客服上级部门名
     */
    @Excel(name = "VIP客服上级部门", sort = 10)
    private String vipAncestorsName;

    /**
     * 运营信息集
     */
    @Excel(name = "运营信息集", sort = 12)
    private String anchorOperateJson;

    /**
     * 运营信息集
     */
    private List<AnchorOperateVo> anchorOperateList;

}