package com.yooa.extend.api.domain.vo;

import com.yooa.extend.api.domain.GoalExtendWeeks;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 *
 */
@Data
public class GoalExtendWeeksVo {

    /**
     * 周开始时间
     */
    private LocalDate beginDate;

    /**
     * 周结束时间
     */
    private LocalDate endDate;

    /**
     * 第几周
     */
    private Integer number;

    /**
     * 审核人id
     */
    private Long auditPeopleId;

    /**
     * 审核人名
     */
    private String auditPeopleName;

    /**
     * 这周计划
     */
    private List<GoalExtendWeeks> weeksList;
}
