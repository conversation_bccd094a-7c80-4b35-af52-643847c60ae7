package com.yooa.extend.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 *
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class GoalExtendDto {

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询id
     */
    private Integer uid;

    /**
     * 查询到哪级
     * 5:员工-专员、4:族长-主管、3:厅长-经理、2:部长-总监、1:会长-副总，总经理，总监
     */
    private Integer userRole;

    /**
     * 类型分别是客服还是推广员
     * type 0:推广员、1:客服
     */
    private Integer type;

}
