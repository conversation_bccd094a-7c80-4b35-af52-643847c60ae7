package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 目标字段名表
 */
@TableName(value = "goal_fields")
@Data
public class GoalFields implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 表id
     */
    @TableId(value = "fields_id", type = IdType.AUTO)
    private Integer fieldsId;
    /**
     * 字段名
     */
    @TableField(value = "fields_name")
    private String fieldsName;
    /**
     * 目标字段对应的完成字段(mybatis表字段、粉丝类型特殊字段多个类型统计在一个字段中)
     */
    @TableField(value = "fields_letter")
    private String fieldsLetter;
}