package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class ScheduleVo {

    /**
     * 目标字段值
     */
    private Long goalFieldsValue;

    /**
     * 完成字段值
     */
    private Long completeFieldsValue;

    /**
     * 字段名
     */
    private String fieldsName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 完成率
     */
    private BigDecimal completion;

    /**
     * sql字段
     */
    private String fieldsLetter;

    /**
     * 完成字段值(对几百粉的使用字段,老粉数)
     */
    private Integer oldCompleteFieldsValue;

    /**
     * 完成字段值(对几百粉的使用字段,新粉数)
     */
    private Integer newCompleteFieldsValue;
}
