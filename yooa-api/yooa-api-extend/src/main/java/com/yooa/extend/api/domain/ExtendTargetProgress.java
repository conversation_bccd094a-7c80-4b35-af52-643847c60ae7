package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 推广每日目标完成统计表
 */
@TableName(value = "extend_target_progress")
@Data
public class ExtendTargetProgress implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 日期
     */
    @TableField(value = "date")
    private LocalDate date;
    /**
     * 推广id
     */
    @TableField(value = "agent_admin_id")
    private Integer agentAdminId;
    /**
     * 优质(推广需要,客服不需要)
     */
    @TableField(value = "quality_users")
    private Integer qualityUsers;
    /**
     * 一交(推广交接给主播:cmf_users_join_live)(推广需要,客服不需要)
     */
    @TableField(value = "receive")
    private Integer receive;
    /**
     * 二交(推广交接给vip:cmf_agent_admin_join_serve)(推广需要,客服不需要)
     */
    @TableField(value = "receive_vip")
    private Integer receiveVip;
    /**
     * 业绩/新增
     */
    @TableField(value = "forty_five_days")
    private BigDecimal fortyFiveDays;
    /**
     * 汇总业绩
     */
    @TableField(value = "real_profit")
    private BigDecimal realProfit;
    /**
     * 充值用户数量
     */
    @TableField(value = "charge_user")
    private Integer chargeUser;
}