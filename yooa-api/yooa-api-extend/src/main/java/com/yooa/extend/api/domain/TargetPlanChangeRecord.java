package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 目标计划变动记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TargetPlanChangeRecord extends BaseEntity {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 打包id
     */
    private Long packId;

    /**
     * 状态
     */
    private String status;

}
