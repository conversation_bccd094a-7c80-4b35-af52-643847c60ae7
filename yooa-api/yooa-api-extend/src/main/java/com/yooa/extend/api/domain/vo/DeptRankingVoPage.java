package com.yooa.extend.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeptRankingVoPage {

    // 部门ID
    private Long deptId;

    // 部门名
    private String deptName;

    // 部门拼接名
    private String ancestorsNames;

    // 部门负责人ID
    private Long userId;

    // 部门负责人名
    private String nickName;

    // 值
    private BigDecimal value;

    // 对值
    private BigDecimal compareValue;

    // 比值
    private BigDecimal scaleValue;

    // 排名
    private Integer rowNum;

    // 总值
    private BigDecimal totalValue;
}
