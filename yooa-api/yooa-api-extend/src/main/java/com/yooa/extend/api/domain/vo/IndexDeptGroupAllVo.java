package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/5 下午4:11
 */
@Data
public class IndexDeptGroupAllVo {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 优质
     */
    private Integer qualityUsers;

    /**
     * 接收数
     */
    private Integer receive;

    /**
     * 接收数vip/接粉数
     */
    private Integer receiveVip;

    /**
     * 业绩/新增
     */
    private BigDecimal fortyFiveDays;

    /**
     * 汇总业绩
     */
    private BigDecimal realProfit;

    /**
     * 充值用户数量
     */
    private Long chargeUser;

    /**
     * 好友
     */
    private Long friend;

    /**
     * 注册
     */
    private Long register;

    /**
     * 首冲
     */
    private Integer firstCharge;

    /**
     * 200粉
     */
    private Integer fans2h;

    /**
     * 500粉
     */
    private Integer fans5h;

    /**
     * 5k粉
     */
    private Integer fans5k;

    /**
     * 5k新粉
     */
    private Integer fans5kNew;

    /**
     * 5w粉
     */
    private Integer fans5w;

    /**
     * 首充率
     */
    private BigDecimal firstChargeRate;

    /**
     * 注册率
     */
    private BigDecimal registerRate;

    /**
     * 交接率
     */
    private BigDecimal receiveRate;

    /**
     * 二交率
     */
    private BigDecimal receiveVipRate;

}
