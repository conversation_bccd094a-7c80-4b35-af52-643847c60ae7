package com.yooa.extend.api.domain.dto;

import com.yooa.extend.api.domain.GoalExtendWeeks;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 *
 */
@Data
public class UpdGoalExtendWeeksDto {

    // 批量修改周计划(一个字段一条,多个字段为一个星期的目标,一个月有多个星期的目标)
    @Size(min = 4, max = 6, message = "数据有误!")
    private List<List<GoalExtendWeeks>> weeksList;

    // 打包id
    private Integer goalId;

    // 制定人备注
    private String producerRemark;

    // 审核状态
    private Integer auditStatus;

}
