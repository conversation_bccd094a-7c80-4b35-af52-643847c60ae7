package com.yooa.extend.api.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 */
@Data
public class ExamineGoalDto {

    // 审核状态(0:待审核、1:审核通过、2:审核未通过)
    private Integer auditStatus;

    // 计划类型(0:个人计划、1:团队计划)
    private Integer goalType;

    // 表类型(0:年计划、1:月计划)
    private Integer tableType;

    // 开始时间
    private LocalDateTime beginTime;

    // 结束时间
    private LocalDateTime endTime;

    // 搜索栏(标题+创建人)
    private String search;

}
