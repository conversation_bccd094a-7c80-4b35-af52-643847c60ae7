package com.yooa.system.api.domain.dto;

import com.yooa.system.api.domain.SysMenu;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@EqualsAndHashCode(callSuper = true)
public class MenuEditDto extends SysMenu {

    @NotNull(message = "菜单ID不能为空")
    private Long menuId;

    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    private String menuName;

    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    private String path;

    @Size(min = 0, max = 200, message = "组件路径不能超过255个字符")
    private String component;

    @NotBlank(message = "菜单类型不能为空")
    private String menuType;
}
