package com.yooa.system.api.domain.vo;

import com.yooa.system.api.domain.SysInterview;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class InterviewVo extends SysInterview {

    private static final long serialVersionUID = 1L;

    /**
     * 邀约用户名称
     */
    private String inviteNickName;

    /**
     * 内推人
     */
    private String channelName;
}