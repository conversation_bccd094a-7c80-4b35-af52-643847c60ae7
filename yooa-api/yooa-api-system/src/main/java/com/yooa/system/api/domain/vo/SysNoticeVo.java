package com.yooa.system.api.domain.vo;

import com.yooa.system.api.domain.SysNotice;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class SysNoticeVo extends SysNotice {

    /**
     * 创建人
     */
    private String createName;

    /**
     * 未读用户数量
     */
    private Long unreadUserNum;

    /**
     * 已读用户数量
     */
    private Long readUserNum;

    /**
     * 总用户数
     */
    private Long totalUserNum;

    /**
     * 已读用户列表
     */
    private List<SysUserNoticeVo> readUserList;

    /**
     * 未读用户列表
     */
    private List<SysUserNoticeVo> unreadUserList;

    /**
     * 统计计划总数
     */
    private int planCount;

    /**
     * 统计其他总数
     */
    private int otherCount;

}
