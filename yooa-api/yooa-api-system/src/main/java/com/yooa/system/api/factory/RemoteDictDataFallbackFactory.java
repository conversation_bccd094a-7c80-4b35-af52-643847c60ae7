package com.yooa.system.api.factory;


import com.yooa.common.core.domain.R;
import com.yooa.system.api.RemoteDictDataService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.query.DictDataQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 字典服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteDictDataFallbackFactory implements FallbackFactory<RemoteDictDataService> {

    @Override
    public RemoteDictDataService create(Throwable throwable) {
        log.error("字典服务调用失败:{}", throwable.getMessage());
        return new RemoteDictDataService() {
            @Override
            public R<List<SysDictData>> getList(DictDataQuery query, String source) {
                return R.fail("查询字典列表失败:" + throwable.getMessage());
            }
        };
    }
}
