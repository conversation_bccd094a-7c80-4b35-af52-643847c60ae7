package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 面试表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysInterview extends BaseEntity {

    /**
     * 面试ID
     */
    @TableId(value = "interview_id", type = IdType.AUTO)
    private Long interviewId;

    /**
     * 面试时间
     */
    private LocalDate interviewDate;

    /**
     * 渠道类型（1BOSS 258 3内推 4返聘）
     */
    private String channelType;

    /**
     * 内推人 (渠道为内推时字段不为空)
     */
    private Long channelId;

    /**
     * 职位类型
     */
    private String position;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 期望薪资
     */
    private BigDecimal salaryExpectation;

    /**
     * 是否住宿（Y是 N否）
     */
    private String isLodging;

    /**
     * 邀约用户ID
     */
    private Long inviteUserId;

    /**
     * 招聘用户ID
     */
    private Long recruitUserId;

    /**
     * 面试姓名
     */
    private String interviewName;

    /**
     * 面试状态
     */
    private String interviewStatus;

    /**
     * 民族
     */
    private String nation;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 婚姻状态
     */
    private String maritalStatus;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别（1男 2女 3其他）
     */
    private String sex;

    /**
     * 学历类型（1初中 2高中 3专科 4本科 5研究生）
     */
    private String educationType;

    /**
     * 学位类型（1学士 2硕士 3博士）
     */
    private String degreeType;

    /**
     * 专业
     */
    private String major;

    /**
     * 出生日期
     */
    private LocalDate birthdayDate;

    /**
     * 农历出生日期
     */
    private String chineseBirthdayDate;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 户籍所在地
     */
    private String registeredResidence;

    /**
     * 现住地址
     */
    private String address;

    /**
     * 健康状态
     */
    private String healthStatus;

    /**
     * 是否有疾病（Y是 N否）
     */
    private String isInfectiousDiseases;

    /**
     * 职业规划
     */
    private String careerPlanning;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人类型
     */
    private String emergencyContactType;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 是否有竞业协议（Y是 N否）
     */
    private String isNca;

    /**
     * 是否有亲属在公司（Y是 N否）
     */
    private String isRelativesHere;
    ;

    /**
     * 亲属姓名
     */
    private String relativesName;

    /**
     * 亲属关系
     */
    private String relativesRelationship;

    /**
     * 是否有犯罪记录 （Y是 N否）
     */
    private String isCriminalRecord;

    /**
     * 是否同意背景调查（Y是 N否）
     */
    private String isBackgroundCheck;

    /**
     * 最看重（1高薪 2晋升 3公司发展 4福利 5行业）
     */
    private String mostConcerned;

    /**
     * 预计入职日期
     */
    private LocalDate estimateEmploymentDate;

    /**
     * 学历扩展信息：
     * enrollment_date 入学日期
     * graduation_date 毕业日期
     * school 学校
     * major 专业
     * education 学历
     * degree 学位
     */
    private String educationExtra;

    /**
     * 工作扩展信息：
     * employment_date 入职日期
     * resignation_date 离职日期
     * company 公司
     * position 职位
     * salary 薪资
     * certifier 证明人
     * certifier_phone 证明人电话
     */
    private String workExtra;

    /**
     * 家庭扩展信息：
     * member_name 成员姓名
     * relationship 关系
     * contact_phone 联系电话
     * work 工作
     * remark 备注
     */
    private String familyExtra;

    /**
     * 行为风格扩展信息
     */
    private String communicationStyleExtra;

    /**
     * 登记表URL
     */
    private String registrationFormUrl;

    /**
     * 面试角色（1总经理 2总监 3经理 4主管 5员工）除了5 其他的都是管理
     */
    private String interviewRole;
}
