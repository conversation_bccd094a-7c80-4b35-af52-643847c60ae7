package com.yooa.system.api.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class CommunicationStyleSaveDto {

    @NotEmpty(message = "行为风格答案列表不能为空")
    @Size(min = 20, max = 20, message = "行为风格答案列表格式不正确")
    private List<List<String>> answerList;

    private Result result;


    /**
     * 结果汇总
     */
    @Data
    public static class Result {
        private Integer d = 0;
        private Integer i = 0;
        private Integer s = 0;
        private Integer c = 0;
        private VerticalResult verticalResult;
        private List<HorizontalResult> horizontalResult;
    }

    /**
     * 竖向结果
     */
    @Data
    public static class VerticalResult {
        private Integer dm = 0;
        private Integer im = 0;
        private Integer sm = 0;
        private Integer cm = 0;
        private Integer dl = 0;
        private Integer il = 0;
        private Integer sl = 0;
        private Integer cl = 0;
    }

    /**
     * 横向结果
     */
    @Data
    public static class HorizontalResult {
        private Integer dm = 0;
        private Integer im = 0;
        private Integer sm = 0;
        private Integer cm = 0;
        private Integer dl = 0;
        private Integer il = 0;
        private Integer sl = 0;
        private Integer cl = 0;
    }
}
