package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 排班表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAttendSchedule extends BaseEntity {

    /**
     * 用户id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 班次id
     */
    private Integer classId;
    /**
     * 考勤组ID
     */
    private String groupId;
    /**
     * 上班时间
     */
    private Date planDate;
    /**
     * 下班时间
     */
    private Date planCheckTime;
    /**
     * 用户Id
     */
    private String userId;
    /**
     * 排班类型
     */
    private String checkType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
