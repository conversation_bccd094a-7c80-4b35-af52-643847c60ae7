package com.yooa.system.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@EqualsAndHashCode
@Data
public class InterviewListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 面试ID
     */
    private Long interviewId;

    /**
     * 面试日期
     */
    @Excel(name = "日期")
    private LocalDate interviewDate;

    /**
     * 面试姓名
     */
    @Excel(name = "姓名")
    private String interviewName;

    /**
     * 性别
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String sex;


    /**
     * 渠道类型
     */
    @Excel(name = "渠道", readConverterExp = "1=BOSS,2=58,3=内推,4=返聘")
    private String channelType;

    /**
     * 职位类型
     */
    @Excel(name = "应聘岗位")
    private String position;

    /**
     * 角色类型
     */
    @Excel(name = "角色", readConverterExp = "1=总经理,2=总监,3=经理,4=主管,5=员工")
    private String roleType;

    /**
     * 期望薪资
     */
    @Excel(name = "期望薪资")
    private BigDecimal salaryExpectation;

    /**
     * 邀约用户ID
     */
    private Long inviteUserId;

    /**
     * 邀约用户昵称
     */
    @Excel(name = "邀约人")
    private String inviteNickName;

    /**
     * 招聘用户ID（复试官）
     */
    private Long recruitUserId;

    /**
     * 招聘用户姓名
     */
    @Excel(name = "复试官")
    private String recruitNickName;

    /**
     * 招聘部门ID
     */
    private Long recruitUserDeptId;

    /**
     * 招聘部门名称
     */
    @Excel(name = "复试官部门")
    private String recruitUserDeptName;

    /**
     * 招聘父部门名称集
     */
    private String recruitUserDeptAncestorsNames;

    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 预计入职时间
     */
    @Excel(name = "预计入职时间")
    private LocalDate estimateEmploymentDate;

    /**
     * 入职时间
     */
    @Excel(name = "入职时间")
    private LocalDate employmentDate;

    /**
     * 面试状态
     */
    @Excel(name = "面试状态", readConverterExp = "1=面试中,2=复试中,3=待入职,4=拒绝入职,5=试用期,6=已转正,7=已离职,8=初试未过,9=复试未过")
    private String interviewStatus;

    /**
     * 行为风格扩展信息
     */
    private String communicationStyleExtra;

    /**
     * 面试登记表URL
     */
    private String registrationFormUrl;

    /**
     * 内推人
     */
    private String channelName;

    /**
     * 员工状态
     */
    private String employeeStatus;
}