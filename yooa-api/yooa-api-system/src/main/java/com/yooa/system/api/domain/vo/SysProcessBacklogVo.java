package com.yooa.system.api.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.SysRecruitCount;
import com.yooa.system.api.domain.SysRecruitUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务流程发起表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysProcessBacklogVo extends BaseEntity{
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;

    /**
     * 申请人ID
     */
    private Long applicantId;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approver;

    /**
     * 标题
     */
    private String title;

    /**
     * 类别（1业务流程 2自定义审批流）
     */
    private Integer type;

    /**
     * 描述
     */
    private String represent;

    /**
     * 类别（流程大类名称）
     */
    private String typeNameBig;

    /**
     * 类别
     */
    private String typeName;

    /**
     * 流程状态
     */
    private String state;

    /**
     * 流程相关字段
     */
    private String formCode;

    private String processNumber;

    private Boolean isOutSideAccessProc;

    private Boolean isLowCodeFlow;

    private String taskId;


    @NotNull(message = "页码不能为空")
    private int page;
    @NotNull(message = "查询数量不能为空")
    private int pageSize;
    /**
     * 1代办 2发起
     */
//    @NotNull(message = "流程类型不能为空")
    private int genre;

    /**
     * 1已提交 2未提交
     */
//    @NotNull(message = "是否提交不能为空")
    private String isSubmit;

    /**
     * 发布时关联的BP人事专员列表
     */
    private List<SysRecruitUser> recruitUserList;

    /**
     * 返回的人事专员列表
     */
    private List<SysRecruitUserVo> recruitUserVoList;

    /**
     * 计划关联的统计数
     */
    private SysRecruitCount sysRecruitCount;

    /**
     * 驳回后重新上传的招聘计划
     */
    private List<SysRecruit> recruitList;

    /**
     * 代办发起列表数据
     */
    List<SysProcessBacklogVo> processBacklogListPage;

    /**
     * 计划数量
     */
    private int planCount;

    /**
     * 总条数
     */
    private int totalCount;

    /**
     * 发起时间
     */
    private LocalDate[] planTime;

    /**
     * 1发布 2保存
     */
    private int operationType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 新增人员信息
     */
    private SysRecruitUser sysRecruitUser;

    /**
     * 删除人员id
     */
    private Long userId;

    /**
     * 发起人部门
     */
    private String operateDeptName;

    /**
     * 流程创建时间
     */
    private LocalDateTime createDate;
}
