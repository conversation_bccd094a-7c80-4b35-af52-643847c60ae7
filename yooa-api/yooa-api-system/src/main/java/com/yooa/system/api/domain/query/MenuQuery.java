package com.yooa.system.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MenuQuery extends QueryEntity {

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 显示状态（0显示 1隐藏）
     */
    private String visible;

    /**
     * 菜单状态（0正常 1停用）
     */
    private String status;
}
