package com.yooa.system.api.domain.vo;

import com.yooa.system.api.domain.SysUserNotice;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserNoticeVo extends SysUserNotice {

    /**
     * 用户id
     */
    private String userName;

    /**
     * 通知id
     */
    private String nickName;

    /**
     * 通知标题
     */
    private String noticeTitle;

    /**
     * 通知详情
     */
    private String noticeContent;

    /**
     * 通知发起时间
     */
    private LocalDateTime sendTime;

    private String createName;

}
