package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/7 14:11
 * @Description:主播详情 (交接粉丝)
 */
@Data
public class AnchorJoinFansVo {


    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 预计消费
     */
    private BigDecimal expectAmt;

    /**
     * 本月消费
     */
    private BigDecimal monthAmt;

    /**
     * 总消费
     */
    private BigDecimal totalAmt;

    /**
     * 接手时间
     */
    private LocalDateTime joinTime;

    /**
     * 接手15天内的新粉标记 (0:是 1:否)
     */
    private Integer newFansFlag;

}
