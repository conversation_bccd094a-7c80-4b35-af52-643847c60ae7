package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户客服交接表（二交）
 */
@Data
public class CrmCustomerJoinServe implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * PD推广id
     */
    private Long extendId;

    /**
     * PD客服id
     */
    private Long serveId;

    /**
     * 交接状态（0待交接 1已交接 2拒绝交接）
     */
    private String status;

    /**
     * 接手时间
     */
    private LocalDateTime joinTime;

    /**
     * 脱手时间
     */
    private LocalDateTime loseTime;

    /**
     * 交接时间
     */
    private LocalDateTime handoverTime;

    /**
     * 接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 流失时间/过期时间
     */
    private LocalDateTime expireTime;

    @TableField(exist = false)
    private Long userId;

    @TableField(exist = false)
    private Long deptId;

    @TableField(exist = false)
    private Long userId1;

    @TableField(exist = false)
    private Long deptId1;
}