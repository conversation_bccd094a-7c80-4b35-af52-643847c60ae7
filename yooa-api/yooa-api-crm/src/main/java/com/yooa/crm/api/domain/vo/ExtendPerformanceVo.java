package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExtendPerformanceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 推广id
     */
    private Long extendId;

    /**
     * 推广用户昵称
     */
    @Excel(name = "推广")
    private String extendName;

    /**
     * 推广部门id
     */
    private Long extendDeptId;

    /**
     * 推广父部门名称集
     */
    @Excel(name = "推广父部门名称集")
    private String extendDeptNames;

    /**
     * 推广部门名称
     */
    @Excel(name = "推广部门名称")
    private String extendDeptName;

    /**
     * 好友数
     */
    @Excel(name = "好友")
    private Integer friendNum;

    /**
     * 注册数
     */
    @Excel(name = "注册")
    private Integer customerNum;

    /**
     * 交接主播数
     */
    @Excel(name = "交接")
    private Integer handoverAnchorNum;

    /**
     * 交接客服数
     */
    @Excel(name = "二交")
    private Integer handoverServeNum;

    /**
     * 有效数
     */
    @Excel(name = "有效")
    private Integer firstChargeNum;

    /**
     * 有效数
     */
    @Excel(name = "200粉")
    private Integer fans2hNum;

    /**
     * 有效数
     */
    @Excel(name = "5K粉")
    private Integer fans5kNum;

    /**
     * 有效数
     */
    @Excel(name = "5W粉")
    private Integer fans5wNum;

    /**
     * 业绩
     */
    @Excel(name = "业绩")
    private BigDecimal orderMoney;

    /**
     * 优质指标
     */
    @Excel(name = "优质指标")
    private Integer qualityNum;
}
