package com.yooa.crm.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.crm.api.factory.RemoteFriendFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 *
 */
@FeignClient(contextId = "remoteWebHookService", value = ServiceNameConstants.CRM_SERVICE, fallbackFactory = RemoteFriendFallbackFactory.class)
public interface RemoteWebHookService {

    String ClassPath = "webhook";

    /**
     * 拉取轮询聊天记录
     * @param source
     * @return
     */
    @GetMapping(ClassPath + "/getChatList")
    R<Integer> GetChatList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
