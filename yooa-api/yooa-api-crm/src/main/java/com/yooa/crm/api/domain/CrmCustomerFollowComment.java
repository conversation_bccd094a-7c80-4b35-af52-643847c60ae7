package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户跟进记录评论表
 */
@Data
public class CrmCustomerFollowComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评论id
     */
    @TableId(value = "comment_id", type = IdType.AUTO)
    private Long commentId;

    /**
     * 父id(顶层只存跟踪记录id、下层存顶层评论id)
     */
    private Long parentId;

    /**
     * 跟踪记录id
     */
    private Long followId;

    /**
     * 评论内容
     */
    private String commentContent;

    /**
     * 评论时间
     */
    private LocalDateTime commentTime;

    /**
     * 评论用户ID
     */
    private Long commentUserId;

    /**
     * 回复用户ID - 被回复
     */
    private Long recoverUserId;

}