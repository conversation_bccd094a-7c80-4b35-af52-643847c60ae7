package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/9 17:42
 * @Description:
 */
@Data
public class HandoverListVo {

    /**
     * 主键id
     */
    private Long handoverId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 昵称
     */
    private String customerNickName;

    /**
     * 交接次数（1首次交接 2多次交接）
     */
    private Integer handoverCount;

    /**
     * 交接状态（0待交接 1已交接 2拒绝交接）
     */
    private Integer handoverStatus;

    /**
     * 拒收理由
     */
    private String refuseMsg;

    /**
     * 交接时间
     */
    private LocalDateTime receiveTime;

    /**
     * 接收时间
     */
    private LocalDateTime handoverTime;

    /**
     * 团队
     */
    private String deptName;

    /**
     * 父部门
     */
    private String ancestorsNames;

    /**
     * 接收人/交接人
     */
    private String receiveName;

    /**
     * 主播姓名
     */
    private String anchorName;

    /**
     * 主播昵称
     */
    private String realAnchorName;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 好友id
     */
    private Long friendId;

    /**
     * 交接类型（1一交 2二交
     */
    private Integer handoverType;

    /**
     * 推广姓名
     */
    private String extendName;

    /**
     * 推广昵称
     */
    private String extendNickName;

    /**
     * 交接图片
     */
    private String handoverImg;

    /**
     * 反馈
     */
    private String handoverInfo;
}
