package com.yooa.crm.api.domain;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 退款表(CrmAnchorRefund)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 15:51:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("crm_customer_refund")
public class CrmCustomerRefund implements Serializable {
    private static final long serialVersionUID = 815667839253507749L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 消费ID
     */
    private Long recordId;

    /**
     * 退款消费创建时间
     */
    private LocalDateTime refundTime;

    /**
     * 操作app管理人
     */
    private Long adminId;

    /**
     * 操作app管理人名字
     */
    private String adminName;

    /**
     * 消费者用户ID
     */
    private Long customerId;

    /**
     * 消费对象ID
     */
    private Long anchorId;

    /**
     * 需要扣除掉的钻石收益
     */
    private Long backCoin;

    /**
     * 退款官方收益
     */
    private Long backOfficialCoin;

    /**
     * 是否全部退款 1是  2否
     */
    private Integer isRefund;

    /**
     * 需要冻结的主播收益
     */
    private Long freezeBackCoin;

    /**
     * 需要冻结的官方收益
     */
    private Long freezeBackOfficialCoin;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
