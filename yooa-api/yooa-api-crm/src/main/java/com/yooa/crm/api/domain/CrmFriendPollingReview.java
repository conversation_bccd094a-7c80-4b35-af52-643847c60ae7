package com.yooa.crm.api.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;

/**
 * 轮询无效好友审核表
 */
@Data
public class CrmFriendPollingReview extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 好友id
     */
    private Long friendId;

    /**
     * 轮询状态1待审核，2已确认，3已驳回，4已追回
     */
    private Long state;
    /**
     * 审核备注
     */
    private String reviewRemark;
}