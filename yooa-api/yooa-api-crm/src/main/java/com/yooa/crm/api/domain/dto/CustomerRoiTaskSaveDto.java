package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmCustomerRoi;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CustomerRoiTaskSaveDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<CrmCustomerRoi> customerRoiList;
}
