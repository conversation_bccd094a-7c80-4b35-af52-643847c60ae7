package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmCustomerFollowComment;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerFollowCommentSaveDto extends CrmCustomerFollowComment {

    @NotNull(message = "评论父id不能为空")
    private Long parentId;

    @NotNull(message = "跟踪记录id不能为空")
    private Long followId;

    @NotBlank(message = "评论内容不能为空")
    private String commentContent;

    @NotNull(message = "回复人id不能为空")
    private Long recoverUserId;
}
