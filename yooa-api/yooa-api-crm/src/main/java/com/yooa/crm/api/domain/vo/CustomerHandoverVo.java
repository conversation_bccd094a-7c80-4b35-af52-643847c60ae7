package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/26 10:36
 * @Description:
 */
@Data
public class CustomerHandoverVo {

    /**
     * 客户id
     */
    //  @Excel(name = "客户id")
    @com.yooa.common.core.annotation.Excel(name = "客户id")
    private Long customerId;

    /**
     * 客户姓名
     */
    // @Excel(name = "客户姓名")
  //  private String customerName;

    /**
     * 昵称
     */
    //  @Excel(name = "昵称")
    @com.yooa.common.core.annotation.Excel(name = "昵称")
    private String friendName;

    /**
     * 交接时间
     */
    // @Excel(name = "交接时间")
    @com.yooa.common.core.annotation.Excel(name = "交接时间" ,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /**
     * 推广姓名
     */
    // @Excel(name = "推广姓名")
    @com.yooa.common.core.annotation.Excel(name = "推广姓名" )
    private String extendUserName;

    /**
     * 推广id
     */
    // @Excel(name = "推广id")
    private Long extendDeptId;

    /**
     * 推广组级列表
     */
    // @Excel(name = "推广组级列表")
    @com.yooa.common.core.annotation.Excel(name = "推广组级列表" )
    private String extendAncestors;

    /**
     * 推广组
     */
    // @Excel(name = "推广组")
    @com.yooa.common.core.annotation.Excel(name = "推广组" )
    private String extendDeptName;


    /**
     * 语种
     */
    // @Excel(name = "语种", replace = {"中文_1","英文_2"})
    @com.yooa.common.core.annotation.Excel(name = "语种",readConverterExp = "1=中文,2=英文")
    private Integer language;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    // @Excel(name = "粉丝类型",replace =  {"男粉_1","女粉_2","英文粉_3","中文粉_4"})
    @com.yooa.common.core.annotation.Excel(name = "粉丝类型",readConverterExp = "1=男粉,2=女粉,3=英文粉,4=中文粉")
    private String fansType;


    /**
     * 投手
     */
    //  @Excel(name = "投手")
    @com.yooa.common.core.annotation.Excel(name = "投手" )
    private String channelNickName;

    /**
     * 渠道
     */
    // @Excel(name = "渠道")
    @com.yooa.common.core.annotation.Excel(name = "渠道" )
    private String channelName;

    /**
     * 好友Id
     */
    private Long friendId;

    /**
     * 领取人id
     */
 //   private Long extendId;

    /**
     * 运营姓名
     */
    @com.yooa.common.core.annotation.Excel(name = "运营姓名")
    private String operateUserName;

    /**
     * 运营id
     */
    private Long operateDeptId;

    /**
     * 运营组级列表
     */
    @com.yooa.common.core.annotation.Excel(name = "运营组级列表")
    private String operateAncestors;

    /**
     * 组名称
     */
    @com.yooa.common.core.annotation.Excel(name = "组名称")
    private String operateDeptName;

    /**
     * 主播姓名
     */
    @com.yooa.common.core.annotation.Excel(name = "主播姓名")
    private String anchorName;


    /**
     * 绑定类型 0:未绑定好友 1:已绑好友
     */
    private String bindType;
}
