package com.yooa.crm.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.crm.api.factory.RemoteCustomerJoinServeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 客户交接客服（二交） - 接口
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteCustomerJoinServeService", value = ServiceNameConstants.CRM_SERVICE, fallbackFactory = RemoteCustomerJoinServeFallbackFactory.class)
public interface RemoteCustomerJoinServeService {

    /**
     * 流失时间/过期时间校正
     */
    @GetMapping("/customer/join/serve/correction-expire-time")
    public R<?> correctionExpireTime(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
