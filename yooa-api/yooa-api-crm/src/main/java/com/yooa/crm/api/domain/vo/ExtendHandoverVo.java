package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/13 13:30
 * @Description:
 */
@Data
public class ExtendHandoverVo extends ServeHandoverVo{

    /**
     * 主播姓名
     */
    @Excel(name = "主播姓名")
    private String anchorName;

    /**
     * 接收人
     */
    @Excel(name = "接收人")
    private String receiveName;

    /**
     * 交接类型（1一交 2二交
     */
    @Excel(name = "交接类型", readConverterExp = "1=一交,2=二交")
    private Integer handoverType;

}
