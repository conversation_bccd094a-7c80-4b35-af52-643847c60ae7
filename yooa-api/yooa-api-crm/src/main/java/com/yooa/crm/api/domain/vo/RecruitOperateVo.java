package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/22 10:12
 * @Description:
 */
@Data
public class RecruitOperateVo {

    /**
     * 招募者
     */
    private String recruitName;


    /**
     * 招募时间
     */
    private LocalDateTime recruitTime;

    /**
     * 招募部门
     */
    private String deptName;

    /**
     * 招募父部门
     */
    private String ancestorsNames;

    /**
     * 招募类型 0:线上 1:线下 2:自招
     */
    private Integer recruitType;

}
