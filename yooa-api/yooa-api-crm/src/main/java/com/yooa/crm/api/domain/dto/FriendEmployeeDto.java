package com.yooa.crm.api.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
@Data
public class FriendEmployeeDto {

    /**
     * 用户id
     */
    @NotNull(message = "用户ID不能为空!")
    private Long userId;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    private Integer fansType;

    /**
     * 开始时间
     */
    @NotNull(message = "时间不能为空!")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @NotNull(message = "时间不能为空!")
    private LocalDateTime endTime;

    /**二次条件，由后端传入**/

    /**
     * 客户ID集
     */
    private List<Long> customerIds;

    /**
     * PD用户ID集
     */
    private List<Long> pdUserId;
}
