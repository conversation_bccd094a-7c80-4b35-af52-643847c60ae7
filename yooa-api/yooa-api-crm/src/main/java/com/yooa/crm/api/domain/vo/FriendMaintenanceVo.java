package com.yooa.crm.api.domain.vo;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/6/18 下午4:41
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 好友维护团队信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FriendMaintenanceVo {

    /**
     * 推广信息
     */
    private List<ExtendVo> extendVoList;

    /**
     * VIP信息
     */
    private List<ServeVo> serveVoList;

    /**
     * 运营信息
     */
    private List<OperatorVo> operatorVoList;

}
