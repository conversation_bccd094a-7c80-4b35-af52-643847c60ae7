package com.yooa.crm.api.domain.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

@Data
public class FriendBrowseRecordQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "好友id不能为空")
    private Long friendId;

    @NotNull(message = "好友浏览记录查询时间不能为空")
    private LocalDate browseDate;

}
