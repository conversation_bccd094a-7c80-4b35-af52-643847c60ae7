package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmCustomer;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;

/**
 *
 */
@Data
@AllArgsConstructor
public class CustomerDto extends CrmCustomer {


    /**
     * 搜索匡(id、客户名、推广、运营、vip负责人)
     */
    private String searchBox;

    // 注册时间开始
    private LocalDate signTimeBegin;

    // 注册时间结束
    private LocalDate signTimeEnd;

    // 修改时间开始
    private LocalDate updateTimeBegin;

    // 修改时间结束
    private LocalDate updateTimeEnd;
}
