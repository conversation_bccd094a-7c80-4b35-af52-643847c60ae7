package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/9 10:15
 * @Description:
 */
@Data
public class AnchorSumDataCollectVo {

    /**
     * 总交接粉丝数
     */
  //  private Long totalJoinFansNum;

    /**
     * 总交接打赏金额
     */
    private BigDecimal totalReward;

    /**
     * 总打赏金额
     */
    private BigDecimal totalRewardMoney;

    /**
     * 总直播时长
     */
    private Double totalLiveHours;

    /**
     * 总交接粉丝
     */
    private Long totalFans;

    /**
     * 交接粉丝打赏率
     */
    private Double totalJoinFansRate;

    /**
     * 总首充
     */
    private Long totalFirstCharge;

    /**
     * 首充转换率
     */
    private Double totalFirstChargeRate;

    /**
     * 打赏率
     */
    private Double avgRewardRate;
}
