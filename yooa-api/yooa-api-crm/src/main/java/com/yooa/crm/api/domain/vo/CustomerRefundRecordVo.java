package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/4 18:26
 * @Description:
 */
@Data
public class CustomerRefundRecordVo {

    /** 订单号 **/
    @Excel(name = "订单号")
    private Long orderNo;

    /** 客户id **/
    @Excel(name = "客户id")
    private Long customerId;

    /** 直播时间 **/
    @Excel(name = "直播时间")
    private LocalDateTime liveTime;

    /** 金额 **/
    @Excel(name = "金额")
    private BigDecimal refundAmount;

    /** 退款时间 **/
    @Excel(name = "退款时间")
    private LocalDateTime refundTime;

    /** 第三方订单id **/
    @Excel(name = "第三方订单id")
    private String thirdOrderNo;

    /** 消费时间 **/
    @Excel(name = "消费时间")
    private LocalDateTime addTime;

    /** 支付方式 **/
    @Excel(name = "支付方式")
    private String paymentType;

}
