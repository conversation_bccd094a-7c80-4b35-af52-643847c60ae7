package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerCostQuery extends QueryEntity {

    private Integer tfType;

    /**
     * 开始时间 - 结束时间
     */
    @NotNull(message = "请选择数据查询开始日期与结束日期")
    private LocalDate[] costDate;

    /**
     * 对公类型（1对公 2对私）
     */
    private String publicType;

    /**
     * 币种
     */
    private String currency;

    /**
     * 一级渠道
     */
    private Long mainChannelId;

    /**
     * 二级渠道
     */
    private Long subChannelId;

    /**
     * 性别
     */
    private String sex;

    /**
     * 语言
     */
    private String language;

    /**
     * 投手id
     */
    private String pitcherId;

    /**
     * 投手昵称
     */
    private String pitcherName;

    /**
     * 投手部门id
     */
    private Long pitcherDeptId;

    /**
     * 推广id
     */
    private Long extendId;

    /**
     * 推广昵称
     */
    private String extendName;

    /**
     * 推广部门id
     */
    private Long extendDeptId;

}
