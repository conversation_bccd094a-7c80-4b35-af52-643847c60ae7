package com.yooa.crm.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.vo.FriendRegisterChargeVo;
import com.yooa.crm.api.factory.RemoteFriendFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 *
 */
@FeignClient(contextId = "remoteFriendService", value = ServiceNameConstants.CRM_SERVICE, fallbackFactory = RemoteFriendFallbackFactory.class)
public interface RemoteFriendService {

    String ClassPath = "friend";

    /**
     * 扫描好友的修改时间是否大于15/30天为更新,由领取变为空闲
     *
     * @param source 请求来源
     */
    @PostMapping(ClassPath + "/ProbationPeriod")
    R<Integer> ProbationPeriod(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询客户ID集关联的好友,好友下的所有客户ID
     *
     * @param ids    OA用户ID集
     * @param source 请求来源
     */
    @GetMapping(ClassPath + "/selCustomerFriendList")
    R<List<CrmCustomerFriend>> selCustomerFriendList(@RequestParam("ids") List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询用户的好友、注册数
     *
     * @param ids    OA用户ID集
     * @param source 请求来源
     */
    @GetMapping(ClassPath + "/getFriendRegister")
    R<List<FriendRegisterChargeVo>> getFriendRegister(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                      @RequestParam(value = "beginTime", required = false) String beginTime,
                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询用户的好友、注册数排名
     *
     * @param ids    OA用户ID集
     * @param source 请求来源
     */
    @GetMapping(ClassPath + "/getFriendRegisterRanking")
    R<Map<String, List<FriendRegisterChargeVo>>> getFriendRegisterRanking(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                                          @RequestParam("beginTime") String beginTime,
                                                                          @RequestParam("endTime") String endTime,
                                                                          @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询部门的好友、注册数
     *
     * @param deptId    OA部门ID
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @GetMapping(ClassPath + "/getFriendRegisterDept")
    R<List<FriendRegisterChargeVo>> getFriendRegisterDept(@RequestParam(value = "deptId", required = false) Long deptId,
                                                          @RequestParam(value = "parentId", required = false) Long parentId,
                                                          @RequestParam(value = "hierarchy", required = false) Integer hierarchy,
                                                          @RequestParam("beginTime") String beginTime,
                                                          @RequestParam("endTime") String endTime,
                                                          @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询部门好友、注册数根据时间分组
     *
     * @param deptId    OA部门ID
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @GetMapping(ClassPath + "/getFriendRegisterDeptTimeGroup")
    R<List<FriendRegisterChargeVo>> getFriendRegisterDeptTimeGroup(@RequestParam("deptId") Long deptId,
                                                                   @RequestParam("beginTime") String beginTime,
                                                                   @RequestParam("endTime") String endTime,
                                                                   @RequestParam("expType") Integer expType,
                                                                   @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询用户好友、注册数根据时间分组
     *
     * @param ids       OA用户ID集
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @GetMapping(ClassPath + "/getFriendRegisterUserTimeGroup")
    R<List<FriendRegisterChargeVo>> getFriendRegisterUserTimeGroup(@RequestParam(value = "ids", required = false) List<Long> ids,
                                                                   @RequestParam("beginTime") String beginTime,
                                                                   @RequestParam("endTime") String endTime,
                                                                   @RequestParam("expType") Integer expType,
                                                                   @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询部门的好友、注册数排名
     *
     * @param hierarchy 部门层级
     * @param beginTime 起始时间
     * @param endTime   结束时间
     */
    @GetMapping(ClassPath + "/getFriendRegisterDeptRanking")
    R<Map<String, List<FriendRegisterChargeVo>>> getFriendRegisterDeptRanking(@RequestParam(value = "findInSetDeptId", required = false) Long findInSetDeptId,
                                                                              @RequestParam("hierarchy") Integer hierarchy,
                                                                              @RequestParam(value = "beginTime", required = false) String beginTime,
                                                                              @RequestParam(value = "endTime", required = false) String endTime,
                                                                              @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
