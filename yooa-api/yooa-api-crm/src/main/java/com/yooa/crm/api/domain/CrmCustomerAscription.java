package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户归属信息表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmCustomerAscription extends BaseEntity {

    /**
     * 客户id
     */
    @TableId(value = "customer_id", type = IdType.AUTO)
    private Long customerId;

    /**
     * PY推广id
     */
    private Long pyExtendId;

    /**
     * PY客服id
     */
    private Long pyServeId;

    /**
     * PY运营id
     */
    private Long pyOperateId;
}