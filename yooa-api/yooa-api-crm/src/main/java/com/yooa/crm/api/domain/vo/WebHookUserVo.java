package com.yooa.crm.api.domain.vo;

import lombok.Data;

/**
 * webhook用户
 */
@Data
public class WebHookUserVo {
    /**
     * 用户id
     */
    private String chat_user_id;

    /**
     *名称
     */
    private String name;

    /**
     *备注名
     */
    private String remark_name;

    /**
     *手机号码
     */
    private String phone;

    /**
     *邮箱
     */
    private String email;
    /**
     *备注
     */
    private String remark;
    /**
     *社媒渠道 1-Messenger 2-聊天插件 3-Email 4-Telegram 5-Instagram 6-Line 7-WhatsApp Api 8-Facebook主页评论 10-Slack 11-企业微信 12-whatsapp 13-instagram评论 15-Telegram App 16-Tiktok App 17-Tiktok评论 18-vkontakte
     */
    private Integer channel;
    /**
     *标签id及标签值 json格式（[{"id":36577,"label_name":"3"}]）
     */
    private String labels;
    /**
     *自定义字段名称及自定义字段值（注意只会返回进行更新的自定义字段，不返回全部自定义字段） json格式（{"custom_field1":36577,"custom_field2":[36577,36566]}）
     */
    private String custom_field;
    /**
     *广告id信息 json格式（{"ad_id":123,"ad_title":"title"}）
     */
    private String ad_referral;
    /**
     *接待成员ID
     */
    private Integer sys_user_id;
    /**
     *创建时间
     */
    private Integer created_time;
    /**
     *更新时间
     */
    private Integer updated_time;
}
