package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class AnchorInfoQuery extends QueryEntity {

    /**
     * 运营部门id
     */
    private Long operateDeptId;

    /**
     * 运营昵称
     */
    private String operateNickName;

    /**
     * 主播名称
     */
    private String anchorName;

    /**
     * 主播类别
     */
    private String anchorType;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 主播风格
     */
    private String anchorStyle;

    /**
     * 语言
     */
    private String language;

    /**
     * 面试状态
     */
    private String anchorStatus;

    /**
     * 性别
     */
    private String sex;

    /**
     * 合作时间
     */
    private LocalDate[] cooperationDate;

    /**
     * 面试时间
     */
    private LocalDate[] interviewDate;

}
