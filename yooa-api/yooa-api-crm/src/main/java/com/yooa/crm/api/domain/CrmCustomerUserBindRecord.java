package com.yooa.crm.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户和用户关联记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrmCustomerUserBindRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    private Long id;
    /**
     * 来源
     */
    private Integer source;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 改绑前用户id
     */
    private Long beforePdUserId;
    /**
     * 改绑后用户id
     */
    private Long afterPdUserId;
    /**
     * 改绑时间
     */
    private LocalDateTime bindTime;
    /**
     * 0:订单、1:用户
     */
    private Integer type;
    /**
     * 0:未处理、1:已处理
     */
    private Integer status;
}