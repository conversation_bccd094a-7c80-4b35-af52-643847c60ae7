package com.yooa.crm.api.domain.vo;

import com.yooa.crm.api.domain.CrmCustomer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class CustomerVo extends CrmCustomer {

    /**
     * 消费者总充值
     */
    private BigDecimal totalUp;

    /**
     * 消费者总充值次数
     */
    private Integer totalUpNumber;

    /**
     * 消费者当月充值
     */
    private BigDecimal monthUp;

    /**
     * 消费者当月充值次数
     */
    private Integer monthUpNumber;

    /**
     * 最后充值时间
     */
    private LocalDateTime lostUpDate;

    /**
     * VIP归属
     */
    private ServeVo serve;

    /**
     * 运营归属
     */
    private List<AnchorOperateVo> anchorOperate;
}
