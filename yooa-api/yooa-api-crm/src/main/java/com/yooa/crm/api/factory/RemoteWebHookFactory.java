package com.yooa.crm.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.crm.api.RemoteWebHookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 *
 */
@Slf4j
@Component
public class RemoteWebHookFactory implements FallbackFactory<RemoteWebHookService> {

    @Override
    public RemoteWebHookService create(Throwable throwable) {
        log.error("调用webhook服务调用失败:{}", throwable.getMessage());
        return new RemoteWebHookService() {

            @Override
            public R<Integer> GetChatList(String source) {
                return R.fail("拉取轮询聊天记录服务调用失败:" + throwable.getMessage());
            }
        };
    }
}
