package com.yooa.cmf.api;

import com.yooa.cmf.api.domain.dto.DataCollectDto;
import com.yooa.cmf.api.domain.vo.ExtendTargetProgressVo;
import com.yooa.cmf.api.factory.RemoteCmfExtendFallbackFactory;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * PD推广服务
 */
@FeignClient(contextId = "remoteCmfExtendService", value = ServiceNameConstants.CMF_SERVICE, fallbackFactory = RemoteCmfExtendFallbackFactory.class)
public interface RemoteCmfExtendService {

    String ClassPath = "pdExtend";

    /**
     * 数据汇总1(推广:在职)只能一天天的查,不能根据时间分组(给定时任务用的)
     *
     * @param dataCollectDto 搜索条件
     * @param source         请求来源
     */
    @PostMapping(ClassPath + "/dataCollect1")
    R<List<ExtendTargetProgressVo>> dataCollect1(@RequestBody DataCollectDto dataCollectDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 数据汇总1(推广:离职)只能一天天的查,不能根据时间分组(给定时任务用的)
     *
     * @param dataCollectDto 搜索条件
     * @param source         请求来源
     */
    @PostMapping(ClassPath + "/dataCollect1f")
    R<List<ExtendTargetProgressVo>> dataCollect1f(@RequestBody DataCollectDto dataCollectDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 数据汇总2(vip)只能一天天的查,不能根据时间分组(给定时任务用的)
     *
     * @param dataCollectDto 搜索条件
     * @param source         请求来源
     */
    @PostMapping(ClassPath + "/dataCollect2")
    public R<List<ExtendTargetProgressVo>> dataCollect2(@RequestBody DataCollectDto dataCollectDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
