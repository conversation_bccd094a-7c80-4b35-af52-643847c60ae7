package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道后台---推广部门表
 */
@TableName(value = "cmf_agent_admin_department")
@Data
public class CmfAgentAdminDepartment implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键 部门唯一ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 部门名称
     */
    @TableField(value = "department_name")
    private String departmentName;
    /**
     * 部门备注
     */
    @TableField(value = "department_remark")
    private String departmentRemark;
    /**
     * 上级部门ID
     */
    @TableField(value = "department_top_id")
    private Integer departmentTopId;
    /**
     * 部门属于的角色ID
     */
    @TableField(value = "department_role")
    private Integer departmentRole;
    /**
     * 部门同级排序 数字越大越在前
     */
    @TableField(value = "department_order")
    private Integer departmentOrder;
    /**
     * 1:推广工会 2:主播公会
     */
    @TableField(value = "department_type")
    private Integer departmentType;
    /**
     * 1 为正常 0为删除
     */
    @TableField(value = "department_status")
    private Integer departmentStatus;
    /**
     * 0 为启用 1为锁定 该字段只有公会生效 锁定后该公会下的所有员工都无法登录cms后台
     */
    @TableField(value = "cmsadmin_lock")
    private Integer cmsadminLock;
    /**
     * 部门创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 部门别称
     */
    @TableField(value = "department_alias")
    private String departmentAlias;
}