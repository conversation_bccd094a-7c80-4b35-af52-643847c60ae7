package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 渠道后台---管理员账号表
 */
@TableName(value = "cmf_agent_admin")
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class CmfAgentAdmin implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 管理员唯一ID 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 管理后台账号
     */
    @TableField(value = "account")
    private String account;
    /**
     * 管理后台密码
     */
    @TableField(value = "password")
    private String password;
    /**
     * 密码盐
     */
    @TableField(value = "salt")
    private String salt;
    /**
     * 备注名
     */
    @TableField(value = "nickname")
    private String nickname;
    /**
     * 账号状态 0禁用 1开启
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 一级部门id
     */
    @TableField(value = "depart_id_first")
    private Integer departIdFirst;
    /**
     * 二级部门id
     */
    @TableField(value = "depart_id_second")
    private Integer departIdSecond;
    /**
     * 三级部门id
     */
    @TableField(value = "depart_id_third")
    private Integer departIdThird;
    /**
     * 四级部门id
     */
    @TableField(value = "depart_id_fourth")
    private Integer departIdFourth;
    /**
     * 所属部门
     */
    @TableField(value = "department_id")
    private Integer departmentId;
    /**
     * 管理员角色
     */
    @TableField(value = "admin_role")
    private Integer adminRole;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Integer createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Integer updateTime;
    /**
     * 头像
     */
    @TableField(value = "avatar")
    private String avatar;
    /**
     * 电子邮箱
     */
    @TableField(value = "email")
    private String email;
    /**
     * 失败次数
     */
    @TableField(value = "loginfailure")
    private Integer loginfailure;
    /**
     * 登录时间
     */
    @TableField(value = "logintime")
    private Integer logintime;
    /**
     * 登录IP
     */
    @TableField(value = "loginip")
    private String loginip;
    /**
     * Session标识
     */
    @TableField(value = "token")
    private String token;
    /**
     * 账号类型 0:管理员  1：会长  2：推广  3：运营
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 推广权重
     */
    @TableField(value = "weigh")
    private Integer weigh;
    /**
     * 推广对应自己的用户id
     */
    @TableField(value = "users_id")
    private Integer usersId;
    /**
     * 热度值
     */
    @TableField(value = "hot")
    private Integer hot;
    /**
     * 邀请码
     */
    @TableField(value = "invitation_code")
    private String invitationCode;
    /**
     * 0:推广 1:客服
     */
    @TableField(value = "types")
    private Integer types;
    /**
     * 推荐位拍卖积分  会长会用到
     */
    @TableField(value = "integral")
    private Integer integral;
}