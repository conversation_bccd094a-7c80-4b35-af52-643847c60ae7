package com.yooa.cmf.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户客服交接VO - 转换字段对应crm_customer_join_serve
 */
@Data
public class ConvertCustomerJoinServeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * PD推广id
     */
    private Long extendId;

    /**
     * PD客服id
     */
    private Long serveId;

    /**
     * 交接状态（0待交接 1已交接 2拒绝交接）
     */
    private String status;

    /**
     * 接手时间
     */
    private LocalDateTime joinTime;
}
