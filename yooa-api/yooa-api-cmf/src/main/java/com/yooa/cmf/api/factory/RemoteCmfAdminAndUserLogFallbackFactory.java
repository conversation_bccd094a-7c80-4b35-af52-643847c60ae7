package com.yooa.cmf.api.factory;

import com.yooa.cmf.api.RemoteCmfAdminAndUserLogService;
import com.yooa.cmf.api.domain.CmfAgentAdminAndUserLog;
import com.yooa.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class RemoteCmfAdminAndUserLogFallbackFactory implements FallbackFactory<RemoteCmfAdminAndUserLogService> {

    @Override
    public RemoteCmfAdminAndUserLogService create(Throwable throwable) {

        log.error("PD平台-推广和用户关联记录服务调用失败: {}", throwable.getMessage());

        return new RemoteCmfAdminAndUserLogService() {
            @Override
            public R<List<CmfAgentAdminAndUserLog>> listByCustomerId(Long customerId, String source) {
                return R.fail("获取推广和用户关联记录数据失败:" + throwable.getMessage());
            }

            @Override
            public R<CmfAgentAdminAndUserLog> lastInfoByCustomerId(Long customerId, String source) {
                return R.fail("获取推广和用户关联记录数据失败:" + throwable.getMessage());
            }


        };
    }
}
