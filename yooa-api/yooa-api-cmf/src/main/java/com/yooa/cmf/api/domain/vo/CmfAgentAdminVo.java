package com.yooa.cmf.api.domain.vo;

import lombok.Data;

/**
 *
 */
@Data
public class CmfAgentAdminVo {
    /**
     * 管理员唯一ID 主键
     */
    private Integer id;

    /**
     * 管理后台账号
     */
    private String account;

    /**
     * 备注名
     */
    private String nickname;

    /**
     * 账号状态 0禁用 1开启
     */
    private Integer status;

    /**
     * 所属部门id
     */
    private Integer departmentId;

    /**
     * 所属部门name
     */
    private String departmentName;

    /**
     * 管理员角色
     */
    private Integer adminRole;

    /**
     * 账号类型 0:管理员  1：会长  2：推广  3：运营
     */
    private Integer type;

    /**
     * 推广对应自己的用户id
     */
    private Integer usersId;

    /**
     * 0:推广 1:客服
     */
    private Integer types;

}