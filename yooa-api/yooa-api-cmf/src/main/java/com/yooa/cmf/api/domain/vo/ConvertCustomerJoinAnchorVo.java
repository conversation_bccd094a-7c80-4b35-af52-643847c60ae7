package com.yooa.cmf.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户主播交接记录VO - 转换字段对应crm_customer_join_anchor
 */
@Data
public class ConvertCustomerJoinAnchorVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * PD推广id
     */
    private Long extendId;

    /**
     * PD运营id
     */
    private Long operateId;

    /**
     * 交接状态（0待交接 1已交接 2拒绝交接）
     */
    private String status;

    /**
     * 交接类型（1首次交接 2二次交接）
     */
    private String type;

    /**
     * 接手时间
     */
    private LocalDateTime joinTime;
}
