package com.yooa.cmf.api.factory;

import com.yooa.cmf.api.RemoteCmfAdminAndUserService;
import com.yooa.cmf.api.domain.CmfAgentAdminAndUser;
import com.yooa.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class RemoteCmfAdminAndUserFallbackFactory implements FallbackFactory<RemoteCmfAdminAndUserService> {

    @Override
    public RemoteCmfAdminAndUserService create(Throwable throwable) {

        log.error("PD平台-推广和用户关联服务调用失败: {}", throwable.getMessage());

        return new RemoteCmfAdminAndUserService() {
            @Override
            public R<List<CmfAgentAdminAndUser>> synchronizationSignBind(String date, String source) {
                return R.fail("获取推广和用户关联数据失败:" + throwable.getMessage());
            }

            @Override
            public R<CmfAgentAdminAndUser> getByCustomerId(Long customerId, String source) {
                return R.fail("查询客户最新的绑定记录失败:" + throwable.getMessage());
            }
        };
    }
}
