package com.yooa.cmf.api.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @TableName cmf_record_back
 */
@Data
public class CmfRecordBack implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 消费ID
     */
    private Long recordId;
    /**
     * 退款消费创建时间
     */
    private LocalDateTime createTime;
    /**
     * 操作app管理人
     */
    private Long adminId;
    /**
     * 操作app管理人名字
     */
    private String adminName;
    /**
     * 消费者用户ID
     */
    private Long uid;
    /**
     * 消费对象ID
     */
    private Long touid;
    /**
     * 需要扣除掉的钻石收益
     */
    private Integer backCoin;
    /**
     * 退款官方收益
     */
    private Integer backOfficialCoin;
    /**
     * 是否全部退款 1是  2否
     */
    private Integer isRefund;
    /**
     * 需要冻结的主播收益
     */
    private Integer freezeBackCoin;
    /**
     * 需要冻结的官方收益
     */
    private Integer freezeBackOfficialCoin;
}