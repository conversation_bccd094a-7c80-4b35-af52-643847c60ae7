package com.yooa.cmf.api;

import com.yooa.cmf.api.domain.CmfAgentAdminAndUserLog;
import com.yooa.cmf.api.factory.RemoteCmfAdminAndUserLogFallbackFactory;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(contextId = "remoteCmfAdminAndUserLogService", value = ServiceNameConstants.CMF_SERVICE, fallbackFactory = RemoteCmfAdminAndUserLogFallbackFactory.class)
public interface RemoteCmfAdminAndUserLogService {

    String ClassPath = "/cmf/admin/user/log";


    @GetMapping(ClassPath + "/list/{customerId}")
    R<List<CmfAgentAdminAndUserLog>> listByCustomerId(@PathVariable("customerId") Long customerId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    @GetMapping(ClassPath + "/last-info/{customerId}")
    R<CmfAgentAdminAndUserLog> lastInfoByCustomerId(@PathVariable("customerId") Long customerId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
