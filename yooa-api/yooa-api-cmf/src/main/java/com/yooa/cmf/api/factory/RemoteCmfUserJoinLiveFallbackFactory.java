package com.yooa.cmf.api.factory;

import com.yooa.cmf.api.RemoteCmfUserJoinLiveService;
import com.yooa.cmf.api.domain.vo.ConvertCustomerJoinAnchorVo;
import com.yooa.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class RemoteCmfUserJoinLiveFallbackFactory implements FallbackFactory<RemoteCmfUserJoinLiveService> {

    @Override
    public RemoteCmfUserJoinLiveService create(Throwable throwable) {

        log.error("PD平台-客户主播交接服务调用失败: {}", throwable.getMessage());

        return new RemoteCmfUserJoinLiveService() {
            @Override
            public R<List<ConvertCustomerJoinAnchorVo>> listByGtId(Long greaterId, String source) {
                return R.fail("获取PD平台客户主播交接数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ConvertCustomerJoinAnchorVo>> listByStatusChange(List<Long> ids, String source) {
                return R.fail("获取PD平台客户主播交接数据失败:" + throwable.getMessage());
            }
        };
    }
}
