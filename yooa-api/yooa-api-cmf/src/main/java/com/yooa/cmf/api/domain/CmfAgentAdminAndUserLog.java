package com.yooa.cmf.api.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 推广用户关联记录表
 *
 * <AUTHOR>
 */
@Data
public class CmfAgentAdminAndUserLog {

    /**
     * 主键
     */
    private Long id;

    /**
     * 推广管理员ID
     */
    private Long adminId;

    /**
     * 与管理员绑定的用户ID
     */
    private Long userId;

    /**
     * 推广申请人ID
     */
    private Long createAdmin;

    /**
     * APP管理后台审核人
     */
    private Long updateUser;

    /**
     * 1 审核通过 0审核中 -1审核不通过
     */
    private Integer status;

    /**
     * 审核备注说明
     */
    private String content;

    /**
     * 审核提交时间
     */
    private LocalDateTime createTime;

    /**
     * 审批时间
     */
    private LocalDateTime updateTime;

    /**
     * 类型 1:申请 2：改绑
     */
    private Integer utype;

    /**
     * clipping_time
     */
    private Long bindingId;

    /**
     * 数据切割时间
     */
    private LocalDateTime clippingTime;
}
