package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取钉钉在职员工列表 - 返回体
 *
 * <AUTHOR>
 */
@Data
public class DingTalkUserNormalListRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询到的员工userid
     */
    private List<String> dataList;

    /**
     * 下一次分页调用的offset值，当返回结果里没有next_cursor时，表示分页结束
     */
    private Integer nextCursor;

}
