package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤班次
 *
 * <AUTHOR>
 */
@Data
public class DingTalkAttendUserCheckRes implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 打卡来源
     * ATM：考勤机打卡（指纹/人脸打卡）
     * BEACON：IBeacon
     * DING_ATM：钉钉考勤机（考勤机蓝牙打卡）
     * USER：用户打卡
     * BOSS：老板改签
     * APPROVE：审批系统
     * SYSTEM：考勤系统
     * AUTO_CHECK：自动打卡
     */
    private String sourceType;
    /**
     * 基准打卡时间
     */
    private Date baseCheckTime;
    /**
     * 用户打卡时间
     */
    private Date userCheckTime;

    /**
     * 定位结果
     * Normal：范围内
     * Outside：范围外
     * NotSigned：未打卡
     */
    private String locationResult;
    /**
     * 打卡结果
     * Normal：正常
     * Early：早退
     * Late：迟到
     * SeriousLate：严重迟到
     * Absenteeism：旷工迟到
     * NotSigned：未打卡
     */
    private String timeResult;
    /**
     * 打卡类型
     * OnDuty：上班
     * OffDuty：下班
     */
    private String checkType;
    /**
     * 考勤日期
     */
    private Date workDate;
}
