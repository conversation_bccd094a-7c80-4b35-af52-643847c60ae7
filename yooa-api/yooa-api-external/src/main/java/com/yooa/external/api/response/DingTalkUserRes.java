package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 钉钉用户
 *
 * <AUTHOR>
 */
@Data
public class DingTalkUserRes implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工的userId
     */
    private String userid;

    /**
     * 员工在当前开发者企业账号范围内的唯一标识
     */
    private String unionid;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 员工的直属主管
     */
    private String manager_userid;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 员工工号
     */
    private String job_number;

    /**
     * 职位
     */
    private String title;

    /**
     * 员工邮箱
     */
    private String email;

    /**
     * 所属部门id列表
     */
    private Long[] dept_id_list;


}
