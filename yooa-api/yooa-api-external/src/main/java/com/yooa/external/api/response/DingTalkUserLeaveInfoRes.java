package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取钉钉员工离职信息 - 返回体
 *
 * <AUTHOR>
 */
@Data
public class DingTalkUserLeaveInfoRes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工userId
     */
    public String userId;

    /**
     * 员工名称
     */
    public String name;

    /**
     * 最后工作日
     */
    public Long lastWorkDay;

    /**
     * 离职原因备注
     */
    public String reasonMemo;

    /**
     * 离职前工作状态：
     * <p>
     * 1：待入职；
     * <p>
     * 2：试用期；
     * <p>
     * 3：正式。
     */
    public Integer preStatus;

    /**
     * 离职交接人的userId
     */
    public String handoverUserId;

    /**
     * 离职状态：
     * <p>
     * 1：待离职。
     * <p>
     * 2：已离职。
     * <p>
     * 3：非待离职或非已离职。
     * <p>
     * 4：已提交离职审批单，审批单暂未通过。
     */
    public Integer status;

    /**
     * 离职前主部门id
     */
    public Long mainDeptId;

    /**
     * 离职前主部门名称
     */
    public String mainDeptName;

    /**
     * 离职被动原因
     */
    public List<String> passiveReason;

    /**
     * 离职主动原因
     */
    public List<String> voluntaryReason;

}
