package com.yooa.external.api;


import com.alibaba.fastjson2.JSONObject;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.factory.RemoteSaleSmartlyPoyoFallbackFactory;
import com.yooa.external.api.response.SaleSmartlyPoyoManageRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 轮询 - 聊天ai训练接口
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteSaleSmartlyPoyoService", url = "https://haitest.poyomanage.com", value = ServiceNameConstants.EXTERNAL_SERVICE, fallbackFactory = RemoteSaleSmartlyPoyoFallbackFactory.class)
public interface RemoteSaleSmartlyPoyoService {

    /**
     * 获取团队管理成员
     */
    @PostMapping("/index/index/saveaidata")
    void saveaidata(@RequestBody SaleSmartlyPoyoManageRes saleSmartlyPoyoManageRes);

}
