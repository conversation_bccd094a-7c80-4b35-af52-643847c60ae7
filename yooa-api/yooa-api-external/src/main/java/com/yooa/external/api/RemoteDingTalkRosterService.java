package com.yooa.external.api;


import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.factory.RemoteDingTalkRosterFallbackFactory;
import com.yooa.external.api.response.DingTalkRosterRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 钉钉花名册 - 远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDingTalkRosterService", value = ServiceNameConstants.EXTERNAL_SERVICE, fallbackFactory = RemoteDingTalkRosterFallbackFactory.class)
public interface RemoteDingTalkRosterService {


    /**
     * 获取钉钉员工花名册详情
     *
     * @param source 请求来源
     * @return 钉钉员工花名册详情
     */
    @GetMapping("/ding-talk/roster/{userId}")
    R<DingTalkRosterRes> getByUserId(@PathVariable("userId") String userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
