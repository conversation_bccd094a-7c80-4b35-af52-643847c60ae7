package com.yooa.external.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 钉钉部门
 *
 * <AUTHOR>
 */
@Data
public class DingTalkDepartmentRes implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父部门id，根部门为1
     */
    private Long parentId;

    /**
     * 部门标识字段
     * 说明：第三方企业应用不返回该参数
     */
    private String sourceIdentifier;

    /**
     * 是否同步创建一个关联此部门的企业群
     * true：创建
     * false：不创建
     */
    private Boolean createDeptGroup;

    /**
     * 当部门群已经创建后，是否有新人加入部门会自动加入该群
     * true：自动加入群
     * false：不会自动加入群
     */
    private Boolean autoAddUser;

    /**
     * 教育部门标签
     * campus：校区
     * period：学段
     * grade：年级
     * class：班级
     * 说明：第三方企业应用不返回该参数
     */
    private String tags;

    /**
     * 部门是否来自关联组织
     * true：是
     * false：不是
     * 说明：第三方企业应用不返回该参数。
     */
    private Boolean fromUnionOrg;

    /**
     * 在父部门中的次序值
     */
    private Long order;

    /**
     * 部门群ID
     */
    private String deptGroupChatId;

    /**
     * 部门群是否包含子部门
     * true：包含
     * false：不包含
     */
    private Boolean groupContainSubDept;

    /**
     * 企业群群主userId
     */
    private String orgDeptOwner;

    /**
     * 部门的主管userd列表
     */
    private List<String> deptManagerUseridList;

    /**
     * 是否限制本部门成员查看通讯录
     * true：开启限制。开启后本部门成员只能看到指定部门/人的通讯录
     * false：不限制
     */
    private Boolean outerDept;

    /**
     * 配置的部门员工可见部门Id列表
     * 说明：接口是否返回该字段，取决于企业是否设置限制本部门成员查看通讯录，即接口返回的outer_dept值
     * 1、限制本部门成员查看通讯录（即outer_dept为true）：outer_permit_depts表示设置的只能看到指定部门/人的部门Id列表
     * 说明：例如，企业开启了限制本部门成员查看通讯录，本部门成员设置了只能看到指定的2个部门、2位员工的通讯录，其中测试部门1的部门Id为1，测试部门2的部门Id为2。调用本接口，获取到设置的部门outer_permit_depts的值为[1,2]。不返回员工列表，即不返回员工小钉1、员工小钉2的信息。
     * 2、未限制本部门成员查看通讯录（即outer_dept为false）：调用接口不返回outer_permit_depts字段
     */
    private List<Long> outerPermitDepts;
    /**
     * 配置的部门员工可见员工userId列表
     * 说明：接口是否返回该字段，取决于企业是否设置限制本部门成员查看通讯录，即接口返回的outer_dept值
     * 1、限制本部门成员查看通讯录（即outer_dept为true）：outer_permit_users表示设置的只能看到指定部门/人的员工userId列表
     * 说明：例如，企业开启了限制本部门成员查看通讯录，本部门成员设置了只能看到指定的2个部门、2位员工的通讯录，其中员工小钉1的userId为001，员工小钉2的userId为002。调用本接口，获取到设置的员工outer_permit_users的值为["001","002"]。不返回部门列表，即不返回测试部门1、测试部门2的信息。
     * 2、未限制本部门成员查看通讯录（即outer_dept为false）：调用接口不返回outer_permit_users字段。
     */
    private List<String> outerPermitUsers;
    /**
     * 隐藏部门的员工userId列表
     * 说明：接口是否返回该字段，取决于企业是否开启隐藏本部门，即接口返回的hide_dept值
     * 1、开启隐藏本部门（即hide_dept为true）：user_permits表示设置的允许指定部门/人可见的员工userId列表
     * 说明：例如，企业开启了隐藏本部门，且分别设置2个部门、2位员工允许指定部门/人可见，其中员工小钉1的userId为001，员工小钉2的userId为002。调用本接口，获取到设置的员工user_permits的值为["001","002"]。不返回部门列表，即不返回测试部门1、测试部门2的信息
     * 2、未开启隐藏本部门（即hide_dept为false）：调用接口不返回user_permits字段
     */
    private List<String> userPermits;
    /**
     * 是否开启隐藏本部门
     * true：开启隐藏本部门。可以设置隐藏范围，如设置向所有人和部门隐藏，或者允许指定部门/人可见
     * false：关闭隐藏本部门，即部门在公司通讯录显示。
     */
    private Boolean hideDept;
    /**
     * 隐藏部门的部门Id列表
     * 说明：接口是否返回该字段，取决于企业是否开启隐藏本部门，即接口返回的hide_dept值
     * 1、开启隐藏本部门（即hide_dept为true）：dept_permits表示设置的允许指定部门/人可见的部门Id列表
     * 说明：例如，企业开启了隐藏本部门，且分别设置2个部门、2位员工允许指定部门/人可见，其中测试部门1的部门Id为1，测试部门2的部门Id为2。调用本接口，获取到设置的部门dept_permits的值为[1,2]。不返回员工列表，即不返回员工小钉1、员工小钉2的信息
     * 2、未开启隐藏本部门（即hide_dept为false）：调用接口不返回dept_permits字段
     */
    private List<Long> deptPermits;
    /**
     * 是否默认同意加入该部门的申请
     * true：表示加入该部门的申请将默认同意
     * false：表示加入该部门的申请需要有权限的管理员同意
     */
    private Boolean autoApproveApply;

    private String brief;

    private String extention;

    private String telephone;

}
