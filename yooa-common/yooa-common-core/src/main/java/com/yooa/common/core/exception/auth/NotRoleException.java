package com.yooa.common.core.exception.auth;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;

/**
 * 未能通过的角色认证异常
 *
 * <AUTHOR>
 */
public class NotRoleException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    public NotRoleException(String role) {
        super(role);
    }

    public NotRoleException(String[] roles) {
        super(CollUtil.join(Arrays.stream(roles).toList(), ","));
    }
}
