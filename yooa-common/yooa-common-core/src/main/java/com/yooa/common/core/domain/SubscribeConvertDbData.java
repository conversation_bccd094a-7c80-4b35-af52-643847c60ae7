package com.yooa.common.core.domain;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.Integer;
import java.util.List;

/**
 * 数据订阅转换
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubscribeConvertDbData {

    /** 数据库表名 **/
    private String dbTableName;

    /** 数据库名 **/
    private String dbName;

    /** 变更类型 1->新增 2->修改 3->删除 **/
    private Integer operatorType;

    private List<Field> fieldList;

    @Data
    public static class Field {
        /** 字段名 **/
        private String fieldName;

        /** 原先值 **/
        private String beforeValue;

        /** 修改后值 **/
        private String afterValue;
    }
}
