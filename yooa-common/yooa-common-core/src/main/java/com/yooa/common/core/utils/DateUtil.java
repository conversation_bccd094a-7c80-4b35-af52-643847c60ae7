package com.yooa.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 *
 */
@Slf4j
public class DateUtil extends cn.hutool.core.date.DateUtil {

    public final static DateFormat yyyy = new SimpleDateFormat("yyyy");
    public final static DateFormat yMd = new SimpleDateFormat("yyyyMMdd");
    public final static DateFormat y_M = new SimpleDateFormat("yyyy-MM");
    public final static DateFormat y_M_d = new SimpleDateFormat("yyyy-MM-dd");
    public final static DateFormat y_M_d_H_m_s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static Integer getDays(Date date) {        // 获取这个月的天数
        LocalDateTime dateTime = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        int month = dateTime.getMonthValue(); // 获取月份
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, month - 1); // 设置月份，January是0
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    public static Date getYearsMonthFirst(Date date) {      // 获得入参的日期,当年的一月一号
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.set(Calendar.MONTH, 0);   // 设置为1月
        c.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天 
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }


    public static Date getMonthFirst(Date date) {
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.add(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天 
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static Date getMonthFinally(Date date) {     // 获取当月最后一天
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DATE, -1);
        return c.getTime();
    }

    public static Date getDayStart(Date date) {  // 清零当天时分秒 格式如"2021-07-08 00:00:00:000"
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }


    /**
     * 获取指定日期 经过指定时间后的日期
     *
     * @param date   指定日期 如果为null，默认使用当前时间
     * @param year   经过的年
     * @param month  经过的月
     * @param day    经过的天
     * @param hour   经过的小时
     * @param minute 经过的分钟
     * @param second 经过的秒
     * @return 经过指定时间后的日期
     */
    public static Date getAfterDate(Date date, int year, int month, int day, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + year);
        // Calendar.Month 0-11
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + month);
        // Calendar.Day_of_year 1-
        calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) + day);
        // Calendar.HOUR_OF_DAY 0-23
        calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + hour);
        // Calendar.MINUTE 0-59
        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + minute);
        // Calendar.SECOND 0-59
        calendar.set(Calendar.SECOND, calendar.get(Calendar.SECOND) + second);
        return calendar.getTime();
    }

    /**
     * 获取指定日期后n天的日期
     *
     * @param offSetDay 天数偏移量
     */
    public static Date getAfterDays(Date date, int offSetDay) {
        return getAfterDate(date, 0, 0, offSetDay, 0, 0, 0);
    }

    public static String dateConvertString(Date date, DateFormat dateFormat) {      // date转string
        return dateFormat.format(date);
    }

    /**
     * 短的转长的会报错
     */
    public static Date stringConvertDate(String str, DateFormat dateFormat) {      // string转date
        try {
            return dateFormat.parse(str);
        }
        catch (ParseException e) {
            log.error("时间转换异常:" + e);
            return new Date();
        }
    }

}
