package com.yooa.common.sensitive.enums;

import com.yooa.common.sensitive.utils.DesensitizedUtil;

import java.util.function.Function;

/**
 * 脱敏类型
 */
public enum DesensitizedType {

    /**
     * 姓名，第2位*替换
     */
    USERNAME(s -> s.replaceAll("(\\S)\\S(\\S*)", "$1*$2")),

    /**
     * 密码，全部字符*替换
     */
    PASSWORD(DesensitizedUtil::password),

    /**
     * 身份证，中间13位*替换
     */
    ID_CARD(DesensitizedUtil::idCard),

    /**
     * 手机号，中间4位*替换
     */
    PHONE(s -> s.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2")),

    /**
     * 电子邮箱，仅显示第一个字母和@后面的地址，其他*替换
     */
    EMAIL(s -> s.replaceAll("(^.)[^@]*(@.*$)", "$1****$2")),

    /**
     * 银行卡号，保留最后4位，其他*替换
     */
    BANK_CARD(DesensitizedUtil::bankCard),

    /**
     * 支付宝账号，全部字符*替换
     */
    ALi_PAY(DesensitizedUtil::aliPay),

    /**
     * 车牌号码，包含普通车辆、新能源车辆
     */
    CAR_LICENSE(DesensitizedUtil::carLicense);

    private final Function<String, String> desensitizer;

    DesensitizedType(Function<String, String> desensitizer) {
        this.desensitizer = desensitizer;
    }

    public Function<String, String> desensitizer() {
        return desensitizer;
    }
}
