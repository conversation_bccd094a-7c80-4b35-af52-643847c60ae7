package com.yooa.common.security.utils;

import com.yooa.common.core.annotation.ExcelDict;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.text.Convert;
import com.yooa.common.core.utils.SpringUtils;
import com.yooa.system.api.RemoteDictDataService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.query.DictDataQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xh
 * @Date: 2025/5/9 13:59
 * @Description:
 */
@Component
@Slf4j
public class ExcelConvertUtil {

    /**
     * 导出时实体类中的类似枚举的字段 0=男 1=女 这种
     * 当前是代码写死在实体类中去匹配  如果字典表发生变化 每一次需要手动去改代码
     * 这里根据查字典表的缓存去实时匹配
     */
    public static void process(List<?> dataList) {
        try {
            if (dataList == null || dataList.isEmpty()) {
                return;
            }

            Set<String> dictTypes = new HashSet<>();
            for (Object obj : dataList) {
                Class<?> clazz = obj.getClass();
                for (Field field : clazz.getDeclaredFields()) {
                    ExcelDict annotation = field.getAnnotation(ExcelDict.class);
                    if (annotation != null) {
                        dictTypes.add(annotation.dictType());
                    }
                }

            }

            RemoteDictDataService remoteDictDataService = SpringUtils.getBean(RemoteDictDataService.class);
            Map<String, Map<String, String>> dictCache = new HashMap<>();
            for (String dictType : dictTypes) {
                DictDataQuery query = new DictDataQuery();
                query.setDictType(dictType);
                List<SysDictData> data = remoteDictDataService.getList(query, SecurityConstants.INNER).getData();
                Map<String, String> valueLabelMap = data.stream()
                        .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
                dictCache.put(dictType, valueLabelMap);
            }

            for (Object obj : dataList) {
                Class<?> clazz = obj.getClass();
                for (Field field : clazz.getDeclaredFields()) {
                    ExcelDict annotation = field.getAnnotation(ExcelDict.class);
                    if (annotation != null) {
                        field.setAccessible(true);
                        Object rawValue = field.get(obj);
                        String dictType = annotation.dictType();
                        Map<String, String> valueLabelMap = dictCache.get(dictType);
                        String strValue = Convert.toStr(rawValue);
                        String convertedValue = valueLabelMap != null ?
                                valueLabelMap.getOrDefault(strValue, strValue) : strValue;
                        field.set(obj, convertedValue);
                    }
                }
            }
        } catch (Exception e) {
            log.error("转换类型时异常 [{}]", e.getMessage());
        }
    }
}
